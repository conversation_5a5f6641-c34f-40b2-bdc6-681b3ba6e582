# simulation.py

import numpy as np
import random
import math # For log2
import matplotlib.pyplot as plt # Import Matplotlib
import time # For timing
import matplotlib.pyplot as plt # Ensure matplotlib is imported
import json # For saving data

# Import comparison algorithms
from comparison_algorithms import gmw_scheduling, eag_scheduling, rand_scheduling

# --- Simulation Parameters (Based on Table I in extracted_sections_copy.tex) ---

# Network Settings
AREA_WIDTH = 100.0  # meters
AREA_HEIGHT = 100.0 # meters
NUM_NODES = 20      # N
NUM_SINKS = 2       # K
COMM_RANGE = 30.0   # meters

# Communication Parameters
BANDWIDTH = 1e6     # W = 1 MHz
NOISE_PSD = -174.0  # dBm/Hz
NOISE_POWER_dBm = NOISE_PSD + 10 * np.log10(BANDWIDTH) # sigma^2 in dBm
NOISE_POWER_mW = 10**(NOISE_POWER_dBm / 10) # sigma^2 in mW
NOISE_POWER_W = NOISE_POWER_mW / 1000 # sigma^2 in W

# Channel Model (<PERSON><PERSON><PERSON>/<PERSON>iis) - Parameters from [Gao2015], [He2013]
RICIAN_K = 10.0
SIGNAL_WAVELENGTH = 0.33 # lambda (meters)
ANTENNA_GAIN_TX_dBi = 8.0
ANTENNA_GAIN_RX_dBi = 2.0
ANTENNA_GAIN_TX = 10**(ANTENNA_GAIN_TX_dBi / 10) # G_i linear
ANTENNA_GAIN_RX = 10**(ANTENNA_GAIN_RX_dBi / 10) # G_j linear
PATH_LOSS_ADJUST_FACTOR = 0.2 # beta
POLARIZATION_LOSS_dB = 1.0
POLARIZATION_LOSS = 10**(POLARIZATION_LOSS_dB / 10) # L_p linear

# SNR and Power
SNR_THRESHOLD_dB = 5.0
SNR_THRESHOLD = 10**(SNR_THRESHOLD_dB / 10) # gamma_min linear
# MODIFIED: Increased minimum transmit power
P_MIN_dBm = 10.0 # Original: 0.0000001
P_MAX_dBm = 20.0
P_MIN_W = 10**(P_MIN_dBm / 10) / 1000 # p_min in Watts
P_MAX_W = 10**(P_MAX_dBm / 10) / 1000 # p_max in Watts

# Energy Consumption (Placeholder for citation needed)
# IMPORTANT: Replace '[需引用]' with actual citation in the paper
# Assuming 1 Mbit = 1e6 bits
# MODIFIED: Increased receive energy cost
P_RCV_J_per_Mbit = 50e-6 # p_rcv (Joules per Mbit) (Original: 50e-6)
# MODIFIED: Increased sensing energy cost
P_SENSE_J_per_Mbit = 60e-6 # p_sense (Joules per Mbit) (Original: 60e-6)
# Convert to Joules per bit for potential use later
P_RCV_J_per_bit = P_RCV_J_per_Mbit / 1e6
P_SENSE_J_per_bit = P_SENSE_J_per_Mbit / 1e6


# Queue Capacity
QUEUE_MAX_Mbit = 50.0 # q_max (Mbit)
QUEUE_MAX_bits = QUEUE_MAX_Mbit * 1e6 # q_max (bits)

# Energy Parameters
BATTERY_MAX_J = 10.0 # b_max (Joules)
INITIAL_ENERGY_MIN_FACTOR = 0.2
INITIAL_ENERGY_MAX_FACTOR = 0.8

# Environment Energy Harvesting (Markov Model - [Ku2015])
# States: 0:Excellent, 1:Good, 2:Fair, 3:Poor
ENV_EH_STATES = 4
# MODIFIED: Halved the mean energy harvesting rates to increase energy scarcity
ENV_EH_MEAN_mW = np.array([10.0, 5.0, 2.0, 0.5]) # mW (Original: [10.0, 5.0, 2.0, 0.5])
ENV_EH_MEAN_W = ENV_EH_MEAN_mW / 1000 # Watts (Average power per slot)
# Assuming unit time slot length (1 second), Power (W) = Energy (J) per slot
# Variances and Transition Matrix from [Ku2015] would be needed for full model
# For simplicity now, we might just use the mean power. Need to clarify later.
# TODO: Implement full Markov model with variances and transitions if needed. (Implementing based on MATLAB code)
ENV_EH_VAR_W2 = np.array([0.71, 1.48, 1.55, 0.31]) * 25e-6 # Variance in W^2, converted from MATLAB (mW/cm^2)^2 assuming 5cm^2 area
ENV_EH_STD_W = np.sqrt(ENV_EH_VAR_W2) # Standard deviation in W

# Transition Matrix P from MATLAB (States 1,2,3,4 map to indices 0,1,2,3)
ENV_EH_TRANSITION_P = np.array([
    [0.938, 0.057, 0.005, 0.000], # From State 0 (Excellent)
    [0.023, 0.955, 0.022, 0.000], # From State 1 (Good)
    [0.000, 0.032, 0.950, 0.018], # From State 2 (Fair)
    [0.004, 0.000, 0.023, 0.973]  # From State 3 (Poor)
])
# Ensure rows sum to 1 (handle potential float precision issues)
ENV_EH_TRANSITION_P = ENV_EH_TRANSITION_P / ENV_EH_TRANSITION_P.sum(axis=1, keepdims=True)


# RF Energy Harvesting (Non-linear Model - [Boshkovska2015])
RF_EH_MU = 1500.0
RF_EH_NU = 0.002
RF_EH_MAX_POWER_W = 0.024 # e_mp (Watts)
RF_EH_OMEGA = 1.0 / (1.0 + np.exp(RF_EH_MU * RF_EH_NU)) # Precompute Omega

# Energy Cooperation Power
E_MIN_mW = 10.0
E_MAX_mW = 100.0
E_MIN_W = E_MIN_mW / 1000 # e_min in Watts
E_MAX_W = E_MAX_mW / 1000 # e_max in Watts

# LTA Energy Constraint
LTA_ENERGY_THRESHOLD_FACTOR = 0.3 # delta_i = 0.3 * b_max
DELTA_I = LTA_ENERGY_THRESHOLD_FACTOR * BATTERY_MAX_J # Precompute delta_i

# Data Arrival
ARRIVAL_PROB = 0.2 # p_arrival (Increased from 0.08)
PACKET_SIZE_MEAN_Mbit = 10.0 # Mean of Exponential distribution (Increased from 1.0)
PACKET_SIZE_MEAN_bits = PACKET_SIZE_MEAN_Mbit * 1e6

# Lyapunov-MEC Algorithm Parameters
V_CONTROL = 100.0 # Default V (will be varied in experiments)
ALPHA_THRESHOLD = 0.3 # Changed from 0.3 to paper default
BETA_PRIME_THRESHOLD = 10.0
GAMMA_THRESHOLD = 0.4
ZETA_PRIME_THRESHOLD = 10.0 # Changed from 1.0 to paper default

# Simulation Control
TOTAL_TIME_SLOTS = 10000 # T (Increased from 1000)
NUM_SIM_RUNS = 30 # Number of independent runs

# --- Node and Network Data Structures ---
# Using simple dictionaries for nodes/sinks for now
# Node structure: {'id': int, 'pos': (x, y), 'is_sink': bool,
#                  'battery': float,
#                  # 'queues': {sink_id: list_of_packet_ids}, # REMOVED: No longer tracking individual packets
#                  'queue_bits': {sink_id: float (bits)}, # Stores total bits for Lyapunov
#                  'energy_deficit_queue': float, 'env_eh_state': int}
# Sink structure: {'id': int, 'pos': (x, y), 'is_sink': True}
nodes = [] # List of node dictionaries
sinks = [] # List of sink dictionaries
all_devices = [] # Combined list of nodes and sinks for easier distance calculation
potential_links = {} # Dictionary: key=sender_id, value=list of receiver_ids within range
avg_channel_gains = {} # Dictionary: key=(sender_id, receiver_id), value=avg_gain

# --- Simulation Functions ---

def calculate_distance(pos1, pos2):
    """Calculates Euclidean distance between two positions."""
    return np.sqrt((pos1[0] - pos2[0])**2 + (pos1[1] - pos2[1])**2)

def initialize_network(max_retries=100):
    """
    Initializes node and sink positions, batteries, queues, links, and avg gains.
    Ensures the generated network topology allows all nodes to reach a sink.
    """
    global nodes, sinks, all_devices, potential_links, avg_channel_gains

    print("Initializing network with a regular grid layout...")
    nodes.clear()
    sinks.clear()
    all_devices.clear()
    potential_links.clear()
    avg_channel_gains.clear()

    # --- Define sink positions: Place sinks in corners ---
    corner_positions = [
        (0, 0),                           # Bottom-left
        (AREA_WIDTH, 0),                  # Bottom-right
        (0, AREA_HEIGHT),                 # Top-left
        (AREA_WIDTH, AREA_HEIGHT)         # Top-right
    ]
    # Add more distinct positions if needed, e.g., midpoints of edges
    if NUM_SINKS > 4:
        corner_positions.extend([
            (AREA_WIDTH / 2, 0),          # Bottom-mid
            (AREA_WIDTH / 2, AREA_HEIGHT),# Top-mid
            (0, AREA_HEIGHT / 2),         # Left-mid
            (AREA_WIDTH, AREA_HEIGHT / 2) # Right-mid
        ])
        # Add center if really needed
        if NUM_SINKS > 8:
             corner_positions.append((AREA_WIDTH / 2, AREA_HEIGHT / 2)) # Center

    # Use the first NUM_SINKS corner positions
    sink_positions = corner_positions[:NUM_SINKS]
    print(f"Placing {NUM_SINKS} sinks in corners/edges: {sink_positions}")

    # Create sink objects using the defined positions
    for i in range(NUM_SINKS):
        sink_id = NUM_NODES + i # Ensure unique IDs
        sink = {
            'id': sink_id,
            'pos': sink_positions[i], # Use the generated positions directly
            'is_sink': True
        }
        sinks.append(sink)
        all_devices.append(sink)
    # --- End of Sink Placement ---

    # Place Nodes in a regular grid
    num_cols = int(np.ceil(np.sqrt(NUM_NODES)))
    num_rows = int(np.ceil(NUM_NODES / num_cols))
    # Adjust spacing to leave some margin
    x_spacing = AREA_WIDTH / (num_cols + 1)
    y_spacing = AREA_HEIGHT / (num_rows + 1)

    node_count = 0
    for r in range(1, num_rows + 1):
        for c in range(1, num_cols + 1):
            if node_count >= NUM_NODES:
                break
            node_id = node_count
            pos_x = c * x_spacing
            pos_y = r * y_spacing
            node = {
                'id': node_id,
                'pos': (pos_x, pos_y),
                'is_sink': False,
                'battery': random.uniform(INITIAL_ENERGY_MIN_FACTOR * BATTERY_MAX_J,
                                          INITIAL_ENERGY_MAX_FACTOR * BATTERY_MAX_J),
                # 'queues': {s['id']: [] for s in sinks}, # REMOVED: No longer tracking individual packets
                'queue_bits': {s['id']: 0.0 for s in sinks}, # Initialize bit counts to zero
                'energy_deficit_queue': 0.0, # B_i(t)
                'env_eh_state': random.randint(0, ENV_EH_STATES - 1) # Initial random state
            }
            nodes.append(node)
            all_devices.append(node)
            node_count += 1
        if node_count >= NUM_NODES:
            break

    # Determine potential links and calculate average channel gains based on grid positions
    # Create a mapping from id to device object for quick lookup
    device_map = {dev['id']: dev for dev in all_devices}
    for dev_i in all_devices:
        # Initialize potential links list for this device if it's a node
        if not dev_i['is_sink']:
            potential_links[dev_i['id']] = []

        for dev_j in all_devices:
            if dev_i['id'] == dev_j['id']:
                continue # No self-loops

            dist = calculate_distance(dev_i['pos'], dev_j['pos'])

            if dist <= COMM_RANGE:
                # Add potential link if sender is a node
                if not dev_i['is_sink']:
                    potential_links[dev_i['id']].append(dev_j['id'])

                # Calculate and store average channel gain g_bar_ij
                if dist > 0:
                     avg_gain = calculate_average_channel_gain(dev_i['pos'], dev_j['pos'])
                     avg_channel_gains[(dev_i['id'], dev_j['id'])] = avg_gain
                     # Store reverse gain as well
                     avg_channel_gains[(dev_j['id'], dev_i['id'])] = avg_gain
                else:
                     avg_channel_gains[(dev_i['id'], dev_j['id'])] = 0
                     avg_channel_gains[(dev_j['id'], dev_i['id'])] = 0
    print(f"Generated {len(nodes)} nodes in a grid layout.")
    print(f"Calculated {len(potential_links)} initial potential sender links.")

    # --- Filter potential_links: Ensure each sink connects to only its closest node ---
    original_potential_links = potential_links.copy() # Keep original for reference if needed
    filtered_potential_links = {node_id: [] for node_id in range(NUM_NODES)} # Initialize for all nodes
    sink_connections = {sink['id']: [] for sink in sinks} # Store potential senders for each sink

    # 1. Separate node-to-node links and identify potential sink senders
    for sender_id, receiver_list in original_potential_links.items():
        for receiver_id in receiver_list:
            receiver_dev = device_map.get(receiver_id)
            if receiver_dev:
                if receiver_dev['is_sink']:
                    sink_connections[receiver_id].append(sender_id)
                else: # Receiver is another node
                    filtered_potential_links[sender_id].append(receiver_id)

    # 2. For each sink, find the closest sender and allow only that link
    num_sink_links_created = 0
    for sink_id, potential_senders in sink_connections.items():
        if not potential_senders:
            continue

        sink_pos = device_map[sink_id]['pos']
        closest_sender_id = -1
        min_dist = float('inf')

        for sender_id in potential_senders:
            sender_pos = device_map[sender_id]['pos']
            dist = calculate_distance(sender_pos, sink_pos)
            if dist < min_dist:
                min_dist = dist
                closest_sender_id = sender_id

        # Add the single allowed link back to the filtered list
        if closest_sender_id != -1:
            filtered_potential_links[closest_sender_id].append(sink_id)
            num_sink_links_created += 1
            # print(f"  Sink {sink_id} connects exclusively to Node {closest_sender_id} (Dist: {min_dist:.2f})")

    # Replace the original potential_links with the filtered version
    potential_links = filtered_potential_links
    print(f"Filtered potential links: Each sink connects to only 1 closest node. Total sink links: {num_sink_links_created}.")
    # Note: avg_channel_gains still contains all original gains. The scheduling logic
    # will effectively ignore gains for links not present in the filtered potential_links.

    # Optional: Re-check connectivity after filtering sink links
    is_connected = check_network_connectivity(nodes, sinks, potential_links)
    if not is_connected:
         print("Warning: Network might be disconnected after restricting sink links. Some nodes may not reach a sink.")
         # Depending on requirements, could raise an error or proceed.


def check_network_connectivity(nodes, sinks, potential_links):
    """
    Checks if all sensor nodes can reach at least one sink via multi-hop paths.
    Uses Breadth-First Search (BFS) starting from all sinks.
    Args:
        nodes: List of node dictionaries.
        sinks: List of sink dictionaries.
        potential_links: Dictionary {sender_id: [receiver_ids]}.
    Returns:
        True if the network is connected to sinks, False otherwise.
    """
    if not nodes:
        return True # No nodes to check connectivity for

    q = [s['id'] for s in sinks] # Start BFS from all sinks
    reachable_nodes = set(q) # Keep track of nodes that can reach a sink (initially just the sinks)
    all_node_ids = {n['id'] for n in nodes}

    head = 0
    while head < len(q):
        current_device_id = q[head]
        head += 1

        # Find nodes that can transmit *to* the current_device_id
        # We need to iterate through all potential senders
        for sender_id, receivers in potential_links.items():
            if current_device_id in receivers:
                # If this sender is a sensor node and hasn't been marked as reachable yet
                if sender_id in all_node_ids and sender_id not in reachable_nodes:
                    reachable_nodes.add(sender_id)
                    q.append(sender_id) # Add the newly reached node to the queue

    # Check if all sensor node IDs are in the set of reachable nodes
    # Need to compare sets of *sensor node* IDs only
    return all_node_ids.issubset(reachable_nodes)


def calculate_average_channel_gain(pos_i, pos_j):
    """Calculates the average channel gain g_bar_ij based on Friis model (Eq. 4)."""
    dist = calculate_distance(pos_i, pos_j)
    if dist <= 0: # Avoid division by zero or invalid distances
        return 0

    # Adjusted Friis formula: G_i * G_j / L_p * (lambda / (4 * pi * (d + beta)))^2
    physical_gain = (ANTENNA_GAIN_TX * ANTENNA_GAIN_RX / POLARIZATION_LOSS) * \
                    (SIGNAL_WAVELENGTH / (4 * np.pi * (dist + PATH_LOSS_ADJUST_FACTOR)))**2

    # --- ARTIFICIAL GAIN BOOST FOR EXPERIMENT ---
    scaling_factor = 15000.0 # Adjust this factor to control the gain level (MODIFIED: Reduced from 15000.0)
    boosted_gain = physical_gain * scaling_factor
    # Cap the gain to avoid unrealistic values > 1 (e.g., cap at 0.5)
    final_gain = min(boosted_gain, 0.5)
    # print(f"Dist: {dist:.2f}m, Physical Gain: {physical_gain:.2E}, Boosted Gain: {final_gain:.4f}") # Optional debug print
    return final_gain

# Note: Removed the duplicate calculate_channel_gain function signature

def get_rician_channel_gain(g_bar_ij):
    """
    Generates an instantaneous channel power gain based on Rician distribution.
    Args:
        g_bar_ij: The average channel power gain (linear).
    Returns:
        Instantaneous channel power gain (linear).
    """
    if g_bar_ij <= 0:
        return 0.0

    # Calculate parameters for the underlying Gaussian distributions
    s_squared = g_bar_ij * RICIAN_K / (RICIAN_K + 1) # Power of LoS
    sigma_squared = g_bar_ij / (2 * (RICIAN_K + 1)) # Variance of scattered components (each Gaussian)

    # Ensure non-negative values before sqrt
    s = np.sqrt(max(0, s_squared))
    sigma = np.sqrt(max(0, sigma_squared))

    # Generate two independent Gaussian random variables
    x = np.random.normal(loc=s, scale=sigma)
    y = np.random.normal(loc=0, scale=sigma)

    # Instantaneous power gain is X^2 + Y^2
    instantaneous_gain = x**2 + y**2
    return instantaneous_gain

def calculate_required_min_power(instantaneous_channel_gain):
    """
    Calculates the minimum transmit power required to meet SNR_THRESHOLD
    for a given instantaneous channel gain, considering P_MIN_W and P_MAX_W.
    Args:
        instantaneous_channel_gain: The current channel gain (linear).
    Returns:
        The required minimum power in Watts, or float('inf') if
        the required power exceeds P_MAX_W or gain is non-positive.
    """
    if instantaneous_channel_gain <= 1e-18: # Avoid division by zero/very small gain
        return float('inf') # Cannot meet SNR threshold with non-positive gain

    # Power needed to meet SNR threshold
    power_for_snr_W = (SNR_THRESHOLD * NOISE_POWER_W) / instantaneous_channel_gain

    # Required power is the max of system minimum and SNR minimum
    required_power_W = max(P_MIN_W, power_for_snr_W)

    # Check if required power exceeds maximum allowed power
    if required_power_W > P_MAX_W:
        return float('inf') # Cannot meet SNR threshold within power limits
    else:
        return required_power_W

def calculate_snr(power_tx_W, instantaneous_channel_gain):
    """Calculates SNR based on transmit power and instantaneous channel gain."""
    if instantaneous_channel_gain <= 0 or NOISE_POWER_W <= 0:
        return 0
    snr = (power_tx_W * instantaneous_channel_gain) / NOISE_POWER_W
    return max(0, snr) # Ensure SNR is non-negative

def calculate_data_rate_bits_per_slot(snr):
    """Calculates data rate in bits per slot using Shannon capacity."""
    if snr <= 0:
        return 0
    # Assuming unit time slot (1 second), Rate (bps) = Capacity
    # Use math.log2 for base 2 logarithm
    try:
        # Ensure argument to log2 is strictly positive
        rate = BANDWIDTH * math.log2(1 + max(snr, 1e-12))
    except ValueError: # Handle potential domain error if 1+snr is negative (shouldn't happen with max)
        rate = 0
    return rate

def environment_harvesting(node):
    """
    Calculates energy harvested from environment for a node based on its
    current EH state using Gaussian distribution.
    """
    current_state = node['env_eh_state']
    mean_power = ENV_EH_MEAN_W[current_state]
    std_dev_power = ENV_EH_STD_W[current_state]

    # Generate random energy based on normal distribution
    harvested_energy = np.random.normal(loc=mean_power, scale=std_dev_power)

    # Ensure harvested energy is non-negative
    harvested_energy = max(0.0, harvested_energy)

    # Assuming unit time slot, Energy (J) = Power (W) * 1s
    return harvested_energy # Joules harvested in this slot

def rf_harvesting(received_rf_power_W):
    """Calculates harvested RF energy using the non-linear model."""
    if received_rf_power_W <= 0:
        return 0.0
    # Eq (9c)
    psi_t = RF_EH_MAX_POWER_W / (1.0 + np.exp(-RF_EH_MU * (received_rf_power_W - RF_EH_NU)))
    # Eq (9a)
    # Add small epsilon to denominator to avoid division by zero if OMEGA is exactly 1
    denominator = 1.0 - RF_EH_OMEGA
    if abs(denominator) < 1e-9:
        denominator = 1e-9 # Prevent division by zero
    harvested_power_W = (psi_t - RF_EH_MAX_POWER_W * RF_EH_OMEGA) / denominator
    # Clamp harvested power to be non-negative and not exceed practical limits (e.g., input power)
    # Also ensure it doesn't exceed the theoretical max e_mp
    harvested_power_W = max(0.0, min(harvested_power_W, received_rf_power_W, RF_EH_MAX_POWER_W))
    # Assuming unit time slot, Energy (J) = Power (W) * 1s
    return harvested_power_W # Joules harvested in this slot

def get_device_by_id(device_id, nodes, sinks):
    """Helper to find a node or sink by its ID."""
    for node in nodes:
        if node['id'] == device_id:
            return node
    for sink in sinks:
        if sink['id'] == device_id:
            return sink
    return None

def calculate_potential_flow(sender_node, receiver_dev, sink_k_id, power_tx_W, channel_gain_ij, e_hat_j):
    """
    Calculates the potential data flow f_lk^t based on constraints.
    Args:
        sender_node: Dictionary of the sending node.
        receiver_dev: Dictionary of the receiving device (node or sink).
        sink_k_id: ID of the final destination sink.
        power_tx_W: Transmit power used.
        channel_gain_ij: Instantaneous channel gain between sender and receiver.
        e_hat_j: Energy cooperation transmission power of the receiver (0 if not transmitting).
    Returns:
        Potential data flow in bits.
    """
    # 1. Calculate Link Capacity (Rate) r_l^t (Eq. 7)
    snr = calculate_snr(power_tx_W, channel_gain_ij)
    rate_bits = calculate_data_rate_bits_per_slot(snr)

    # 2. Sender Queue Constraint f_lk^t <= q_ik^t (Eq. 8b) - Use queue_bits
    sender_queue_bits = sender_node['queue_bits'].get(sink_k_id, 0.0)

    # 3. Receiver Constraints (only apply if receiver is a node, not a sink)
    receiver_energy_constraint_bits = float('inf')
    receiver_buffer_constraint_bits = float('inf')

    if not receiver_dev['is_sink']:
        # Receiver Energy Constraint f_lk^t <= (b_j^t - e_hat_j) / p_rcv (Eq. 8c, adapted)
        # Ensure receiver has enough energy *after* its own energy transmission
        available_energy_for_reception = max(0, receiver_dev['battery'] - e_hat_j)
        if P_RCV_J_per_bit > 1e-12: # Avoid division by zero if p_rcv is effectively 0
             # Use floor as we can't receive fractional bits based on energy
             receiver_energy_constraint_bits = math.floor(available_energy_for_reception / P_RCV_J_per_bit)
        else:
              receiver_energy_constraint_bits = float('inf')

        # Receiver Buffer Constraint f_lk^t <= q_max - sum(q_jk') (Eq. 8d)
        # Use queue_bits to get the current total bits in the receiver's queues
        current_total_queue_j = sum(receiver_dev['queue_bits'].values())
        receiver_buffer_constraint_bits = max(0, QUEUE_MAX_bits - current_total_queue_j)

    # Determine the actual flow based on the minimum of all constraints
    potential_flow_bits = min(rate_bits,
                              sender_queue_bits,
                              receiver_energy_constraint_bits,
                              receiver_buffer_constraint_bits)

    # Ensure flow is non-negative and realistically achievable (e.g., cannot exceed rate)
    return max(0, potential_flow_bits)


def lyapunov_mec_scheduling(current_slot, nodes, sinks, potential_links, current_channel_gains): # Ensure packet_details is not passed
    """
    Performs the Lyapunov-MEC scheduling for the current time slot.
    Implements logic from Algorithm 1 and 2 in the paper.
    Args:
        current_slot: The current time slot index (t).
        nodes: List of node dictionaries (mutable state).
        sinks: List of sink dictionaries.
        potential_links: Dict {sender_id: [receiver_ids]}.
        current_channel_gains: Dict {(sender_id, receiver_id): inst_gain}.
    Returns:
        A dictionary containing the decisions for this timeslot:
        {
            'y': {node_id: 0 or 1},              # Energy Tx state (1=Tx, 0=Rx)
            'e_hat': {node_id: power_W},         # Energy Tx power
            'x': {(i, j, k): 0 or 1},            # Link activation for sink k
            'p': {(i, j): power_W},              # Data Tx power
            'f': {(i, j, k): flow_bits}          # Data flow
        }
    """
    # Initialize decisions
    y_decisions = {node['id']: 0 for node in nodes}
    e_hat_decisions = {node['id']: 0.0 for node in nodes}
    x_decisions = {} # Will be populated with (i, j, k): 1
    p_decisions = {} # Will be populated with (i, j): power
    f_decisions = {} # Will be populated with (i, j, k): bits

    # --- Phase 1: Energy Cooperation Decision (Algorithm 1) ---
    available_energy_senders_ids = [node['id'] for node in nodes] # IDs of nodes initially available
    processed_ec_senders = set() # Keep track of nodes chosen to send energy in this slot

    while True:
        best_gain = -float('inf')
        best_sender_id = -1
        best_power_W = 0.0

        # Find senders not yet processed in this phase
        current_potential_senders_ids = [idx for idx in available_energy_senders_ids if idx not in processed_ec_senders]
        if not current_potential_senders_ids:
            break # No more available senders to check

        for sender_id in current_potential_senders_ids:
            sender_node = get_device_by_id(sender_id, nodes, sinks)
            if not sender_node: continue

            # Check sender conditions (Sec V.A.1 step 1)
            sender_batt_ok = sender_node['battery'] > ALPHA_THRESHOLD * BATTERY_MAX_J
            sender_deficit_ok = sender_node['energy_deficit_queue'] < BETA_PRIME_THRESHOLD
            is_potential_sender = sender_batt_ok and sender_deficit_ok
            if not is_potential_sender:
                continue

            # Check if any neighbor is a potential receiver (Sec V.A.1 step 2)
            potential_receivers_exist = False
            potential_receiver_ids = []
            if sender_id in potential_links:
                for neighbor_id in potential_links[sender_id]:
                    neighbor_dev = get_device_by_id(neighbor_id, nodes, sinks)
                    # Only consider nodes as potential receivers
                    if neighbor_dev and not neighbor_dev['is_sink']:
                         receiver_batt_ok = neighbor_dev['battery'] < GAMMA_THRESHOLD * BATTERY_MAX_J
                         receiver_deficit_ok = neighbor_dev['energy_deficit_queue'] > ZETA_PRIME_THRESHOLD
                         is_potential_receiver = receiver_batt_ok and receiver_deficit_ok
                         if is_potential_receiver:
                              potential_receivers_exist = True
                              potential_receiver_ids.append(neighbor_id)
                              # break # Found at least one - Let's find all potential receivers for gain calc

            if not potential_receivers_exist:
                continue

            # Evaluate gain for different trial powers (e.g., min and max) (Sec V.A.1 step 3)
            # Using a simplified evaluation: check gain at E_MIN_W and E_MAX_W
            for e_trial_W in [E_MIN_W, E_MAX_W]:
                # Check if sender can afford this power
                if e_trial_W < E_MIN_W or e_trial_W > E_MAX_W or e_trial_W > sender_node['battery']:
                    continue

                # Calculate estimated gain Delta W_i^EC (Eq. 16)
                # Simplified estimation: assumes only this sender transmits energy
                # Uses average channel gain for estimation stability
                estimated_gain = 0.0
                if sender_id in potential_links:
                    for neighbor_id in potential_links[sender_id]:
                        neighbor_dev = get_device_by_id(neighbor_id, nodes, sinks)
                        # Only nodes can receive energy in this model
                        if neighbor_dev and not neighbor_dev['is_sink']:
                             # Check if neighbor is a potential receiver (as defined above)
                             is_potential_receiver = (neighbor_dev['battery'] < GAMMA_THRESHOLD * BATTERY_MAX_J and
                                                      neighbor_dev['energy_deficit_queue'] > ZETA_PRIME_THRESHOLD)
                             if is_potential_receiver:
                                 avg_gain_ij = avg_channel_gains.get((sender_id, neighbor_id), 0.0)
                                 # Simplified RF power at receiver (ignoring others)
                                 rf_power_at_j = e_trial_W * avg_gain_ij
                                 # Estimate harvested energy using the rf_harvesting function
                                 delta_e_check_j = rf_harvesting(rf_power_at_j)
                                 weighted_benefit_term = neighbor_dev['energy_deficit_queue'] * delta_e_check_j
                                 weighted_benefit_term = neighbor_dev['energy_deficit_queue'] * delta_e_check_j
                                 estimated_gain += weighted_benefit_term


                # Subtract weighted cost of transmission
                cost_term = sender_node['energy_deficit_queue'] * e_trial_W
                estimated_gain -= cost_term


                # Update best gain found so far for any sender
                if estimated_gain > best_gain:
                    best_gain = estimated_gain
                    best_sender_id = sender_id
                    best_power_W = e_trial_W

        # Make decision for this iteration (Sec V.A.1 step 4)
        if best_gain > 0 and best_sender_id != -1:
            # Set decisions for the best sender found in this iteration
            y_decisions[best_sender_id] = 1
            e_hat_decisions[best_sender_id] = best_power_W
            processed_ec_senders.add(best_sender_id) # Mark as processed for EC this slot
            # print(f"  EC Decision: Node {best_sender_id} transmits {best_power_W:.4f} W (Gain: {best_gain:.4f})") # Original print
        else:
            # No more beneficial energy cooperation found in this iteration
            break

    # --- Phase 2: Data Transmission Decision (Algorithm 2) ---
    nodes_data_busy = set() # Nodes involved in data Tx/Rx this slot
    candidate_links_tuples = [] # List of ((i, j, k), NetWeight, f_potential_bits)

    # 1. Calculate NetWeight for all potential links (Sec V.A.2 steps 1-2)
    for sender_id in range(NUM_NODES):
        sender_node = get_device_by_id(sender_id, nodes, sinks)
        # Skip if node doesn't exist or has no potential outgoing links
        if not sender_node or sender_id not in potential_links: continue

        e_hat_i = e_hat_decisions[sender_id] # Energy Tx power decided in Phase 1

        # Basic energy check: Can sender afford p_min + its own energy Tx?
        if sender_node['battery'] < P_MIN_W + e_hat_i:
            continue

        for receiver_id in potential_links[sender_id]:
            receiver_dev = get_device_by_id(receiver_id, nodes, sinks)
            if not receiver_dev: continue

            e_hat_j = e_hat_decisions.get(receiver_id, 0.0) # Receiver's energy Tx power (0 if sink or not Tx)
            # Get instantaneous channel gain for this link
            channel_gain_ij = current_channel_gains.get((sender_id, receiver_id), 0.0)

            if channel_gain_ij <= 0: continue # Skip if channel gain is non-positive

            # Check all destination sinks k for which sender i has data
            for sink_k_id in sender_node['queue_bits']: # Iterate using queue_bits keys which represent sinks with potential data
                # REMOVED: Check for non-empty packet list (sender_node['queues'])
                # Now, we only care if queue_bits > 0, which is implicitly handled by calculate_potential_flow
                # MODIFIED: Calculate required minimum power instead of using fixed P_MIN_W
                p_required_min_W = calculate_required_min_power(channel_gain_ij)

                # Skip if required power is infinite (cannot meet SNR or exceeds P_MAX_W)
                if p_required_min_W == float('inf'):
                    continue

                # Check if sender can afford this required power + its energy Tx
                if sender_node['battery'] < p_required_min_W + e_hat_i:
                    continue

                # Calculate potential flow using the required minimum power
                f_potential_bits = calculate_potential_flow(sender_node, receiver_dev, sink_k_id, p_required_min_W, channel_gain_ij, e_hat_j)

                # Only consider if potential flow is positive
                if f_potential_bits > 1e-9:
                    # Calculate Link Weight W_lk (Eq. 17) (step 1) - Use queue_bits
                    q_ik = sender_node['queue_bits'].get(sink_k_id, 0.0) # Get sender queue size in bits
                    q_jk = 0.0 # Sink queue is 0
                    if not receiver_dev['is_sink']:
                        q_jk = receiver_dev['queue_bits'].get(sink_k_id, 0.0) # Get receiver queue size in bits
                    is_final_hop = (receiver_id == sink_k_id)
                    W_lk = q_ik - q_jk + (V_CONTROL if is_final_hop else 0.0)

                    # Calculate Net Weight (Eq. 18) (step 2) - Use p_required_min_W
                    B_i = sender_node['energy_deficit_queue']
                    B_j = 0.0
                    receive_energy_cost_J = 0.0
                    if not receiver_dev['is_sink']:
                        B_j = receiver_dev['energy_deficit_queue']
                        receive_energy_cost_J = P_RCV_J_per_bit * f_potential_bits

                    # Note: Weight calculation uses potential flow and required power
                    net_weight = (W_lk * f_potential_bits) - (B_i * p_required_min_W) - (B_j * receive_energy_cost_J)

                    # Add to candidates list, store required power with the candidate
                    candidate_links_tuples.append(((sender_id, receiver_id, sink_k_id), net_weight, f_potential_bits, p_required_min_W))

    # 2. Sort candidates by NetWeight (step 2c)
    candidate_links_tuples.sort(key=lambda x: x[1], reverse=True)

    # 3. Iteratively select links (step 2d onwards)
    for candidate in candidate_links_tuples:
        # Unpack candidate tuple, now includes required power
        (i, j, k), weight, flow_bits, p_req_W = candidate # flow_bits is f_potential calculated earlier

        # Check if net weight is positive (step 2h - using pre-calculated weight)
        if weight <= 0:
            continue # Skip non-positive weights based on initial calculation

        sender_node = get_device_by_id(i, nodes, sinks)
        receiver_dev = get_device_by_id(j, nodes, sinks)

        # Check half-duplex constraint (step 2e)
        sender_busy = i in nodes_data_busy
        # Receiver is busy only if it's a node and already involved
        receiver_busy = (not receiver_dev['is_sink']) and (j in nodes_data_busy)

        if not sender_busy and not receiver_busy:
            # MODIFIED: Use the required power calculated earlier
            p_actual_W = p_req_W # Use the power stored with the candidate
            e_hat_i = e_hat_decisions[i]
            e_hat_j = e_hat_decisions.get(j, 0.0)

            # Final combined energy checks (step 2g) - Use p_actual_W
            # Re-check sender energy just before activation
            sender_energy_ok = sender_node['battery'] >= p_actual_W + e_hat_i
            receiver_energy_ok = True
            if not receiver_dev['is_sink']:
                 # Re-check receiver energy based on the flow we intend to send
                 # Note: flow_bits was calculated using p_actual_W (p_req_W)
                 required_rcv_energy = P_RCV_J_per_bit * flow_bits
                 receiver_energy_ok = receiver_dev['battery'] >= required_rcv_energy + e_hat_j

            # Activate link if all checks pass (step 2h)
            # Re-calculate final net weight using actual flow and power just to be sure?
            # Or trust the initial calculation if flow didn't change. Let's trust initial weight > 0 check.
            # MODIFIED: Removed packet selection logic. Directly use flow_bits if checks pass.
            if sender_energy_ok and receiver_energy_ok and flow_bits > 1e-9: # flow_bits is potential bits
                # Set decisions directly using the calculated potential flow_bits
                link_key = (i, j, k)
                power_key = (i, j)
                x_decisions[link_key] = 1
                # Store power only once per physical link (i,j) - Use p_actual_W
                if power_key not in p_decisions:
                     p_decisions[power_key] = p_actual_W
                # Store the calculated potential flow in bits
                f_decisions[link_key] = flow_bits # Store the calculated bits directly

                # Mark nodes as busy for data Tx/Rx this slot (step 2i)
                nodes_data_busy.add(i)
                if not receiver_dev['is_sink']:
                    nodes_data_busy.add(j)
                # print(f"  DT Decision: Link ({i}->{j}) Sink {k}, Bits: {flow_bits:.2f}, Power: {p_actual_W:.4f} W, Weight: {weight:.2f}")

    # Consolidate decisions into the return dictionary
    actions = {
        'y': y_decisions,
        'e_hat': e_hat_decisions,
        'x': x_decisions,
        'p': p_decisions,
        'f': f_decisions
    }
    return actions

# --- NEW FUNCTION: Lyapunov-NoEC Scheduling ---
def lyapunov_noec_scheduling(current_slot, nodes, sinks, potential_links, current_channel_gains): # Ensure packet_details is not passed
    """
    Performs the Lyapunov scheduling WITHOUT Energy Cooperation (Lyapunov-NoEC).
    Only performs Phase 2 (Data Transmission) decision, assuming no EC.
    Args:
        current_slot: The current time slot index (t).
        nodes: List of node dictionaries (mutable state).
        sinks: List of sink dictionaries.
        potential_links: Dict {sender_id: [receiver_ids]}.
        current_channel_gains: Dict {(sender_id, receiver_id): inst_gain}.
    Returns:
        A dictionary containing the decisions for this timeslot (y and e_hat are always 0):
        {
            'y': {node_id: 0},                   # Energy Tx state (always 0)
            'e_hat': {node_id: 0.0},             # Energy Tx power (always 0)
            'x': {(i, j, k): 0 or 1},            # Link activation for sink k
            'p': {(i, j): power_W},              # Data Tx power
            'f': {(i, j, k): flow_bits}          # Data flow
        }
    """
    # Initialize decisions - No Energy Cooperation
    y_decisions = {node['id']: 0 for node in nodes}
    e_hat_decisions = {node['id']: 0.0 for node in nodes} # Always 0 for NoEC
    x_decisions = {} # Will be populated with (i, j, k): 1
    p_decisions = {} # Will be populated with (i, j): power
    f_decisions = {} # Will be populated with (i, j, k): bits

    # --- Phase 1: Skipped for NoEC ---

    # --- Phase 2: Data Transmission Decision (Adapted for NoEC) ---
    nodes_data_busy = set() # Nodes involved in data Tx/Rx this slot
    candidate_links_tuples = [] # List of ((i, j, k), NetWeight, f_potential_bits)

    # 1. Calculate NetWeight for all potential links
    for sender_id in range(NUM_NODES):
        sender_node = get_device_by_id(sender_id, nodes, sinks)
        if not sender_node or sender_id not in potential_links: continue

        # e_hat_i is always 0 for NoEC
        e_hat_i = 0.0

        # Basic energy check: Can sender afford p_min?
        if sender_node['battery'] < P_MIN_W: # Simplified check for NoEC
            continue

        for receiver_id in potential_links[sender_id]:
            receiver_dev = get_device_by_id(receiver_id, nodes, sinks)
            if not receiver_dev: continue

            # e_hat_j is always 0 for NoEC
            e_hat_j = 0.0
            channel_gain_ij = current_channel_gains.get((sender_id, receiver_id), 0.0)

            if channel_gain_ij <= 0: continue

            for sink_k_id in sender_node['queue_bits']: # Iterate using queue_bits keys
                # REMOVED: Check for non-empty packet list (sender_node['queues'])
                # Now, we only care if queue_bits > 0, which is implicitly handled by calculate_potential_flow
                # MODIFIED: Calculate required minimum power instead of using fixed P_MIN_W
                p_required_min_W = calculate_required_min_power(channel_gain_ij)

                # Skip if required power is infinite or sender cannot afford it
                if p_required_min_W == float('inf') or sender_node['battery'] < p_required_min_W:
                    continue

                # Calculate potential flow using the required minimum power, assuming e_hat_j = 0
                f_potential_bits = calculate_potential_flow(sender_node, receiver_dev, sink_k_id, p_required_min_W, channel_gain_ij, e_hat_j=0.0)

                if f_potential_bits > 1e-9:
                    # Calculate Link Weight W_lk (Eq. 17) - Use queue_bits
                    q_ik = sender_node['queue_bits'].get(sink_k_id, 0.0) # Get sender queue size in bits
                    q_jk = 0.0
                    if not receiver_dev['is_sink']:
                        q_jk = receiver_dev['queue_bits'].get(sink_k_id, 0.0) # Get receiver queue size in bits
                    is_final_hop = (receiver_id == sink_k_id)
                    W_lk = q_ik - q_jk + (V_CONTROL if is_final_hop else 0.0)

                    B_i = sender_node['energy_deficit_queue']
                    B_j = 0.0
                    receive_energy_cost_J = 0.0
                    if not receiver_dev['is_sink']:
                        B_j = receiver_dev['energy_deficit_queue']
                        receive_energy_cost_J = P_RCV_J_per_bit * f_potential_bits

                    # Net Weight calculation for NoEC (no e_hat terms) - Use p_required_min_W
                    net_weight = (W_lk * f_potential_bits) - (B_i * p_required_min_W) - (B_j * receive_energy_cost_J)

                    # Store required power with the candidate
                    candidate_links_tuples.append(((sender_id, receiver_id, sink_k_id), net_weight, f_potential_bits, p_required_min_W))

    # 2. Sort candidates by NetWeight
    candidate_links_tuples.sort(key=lambda x: x[1], reverse=True)

    # 3. Iteratively select links
    for candidate in candidate_links_tuples:
        # Unpack candidate tuple, now includes required power
        (i, j, k), weight, flow_bits, p_req_W = candidate

        if weight <= 0:
            continue

        sender_node = get_device_by_id(i, nodes, sinks)
        receiver_dev = get_device_by_id(j, nodes, sinks)

        sender_busy = i in nodes_data_busy
        receiver_busy = (not receiver_dev['is_sink']) and (j in nodes_data_busy)

        if not sender_busy and not receiver_busy:
            # MODIFIED: Use the required power calculated earlier
            p_actual_W = p_req_W # Use the power stored with the candidate
            # e_hat_i and e_hat_j are 0 for NoEC

            # Final energy checks for NoEC - Use p_actual_W
            sender_energy_ok = sender_node['battery'] >= p_actual_W # Check only data Tx power
            receiver_energy_ok = True
            if not receiver_dev['is_sink']:
                 required_rcv_energy = P_RCV_J_per_bit * flow_bits
                 receiver_energy_ok = receiver_dev['battery'] >= required_rcv_energy # Check only data Rx energy

            # MODIFIED: Removed packet selection logic. Directly use flow_bits if checks pass.
            if sender_energy_ok and receiver_energy_ok and flow_bits > 1e-9: # flow_bits is potential bits
                # Set decisions directly using the calculated potential flow_bits
                link_key = (i, j, k)
                power_key = (i, j)
                x_decisions[link_key] = 1
                if power_key not in p_decisions:
                     p_decisions[power_key] = p_actual_W
                # Store the calculated potential flow in bits
                f_decisions[link_key] = flow_bits # Store the calculated bits directly

                nodes_data_busy.add(i)
                if not receiver_dev['is_sink']:
                    nodes_data_busy.add(j)

    # Consolidate decisions into the return dictionary
    actions = {
        'y': y_decisions,       # Always 0
        'e_hat': e_hat_decisions, # Always 0
        'x': x_decisions,
        'p': p_decisions,
        'f': f_decisions
    }
    return actions

# --- NEW FUNCTION: Lyapunov-UEC Scheduling (Unicast Energy Cooperation) ---
def lyapunov_uec_scheduling(current_slot, nodes, sinks, potential_links, current_channel_gains):
    """
    Performs the Lyapunov scheduling WITH Unicast Energy Cooperation (Lyapunov-UEC).
    Phase 1 is modified for unicast decisions. Phase 2 (Data Tx) is similar to MEC.
    Args:
        current_slot: The current time slot index (t).
        nodes: List of node dictionaries (mutable state).
        sinks: List of sink dictionaries.
        potential_links: Dict {sender_id: [receiver_ids]}.
        current_channel_gains: Dict {(sender_id, receiver_id): inst_gain}.
    Returns:
        A dictionary containing the decisions for this timeslot.
    """
    # Initialize decisions
    y_decisions = {node['id']: 0 for node in nodes}
    e_hat_decisions = {node['id']: 0.0 for node in nodes}
    x_decisions = {} 
    p_decisions = {} 
    f_decisions = {} 

    # --- Phase 1: Unicast Energy Cooperation Decision (Heuristic: Send to Lowest Energy Neighbor) ---
    nodes_ec_busy = set() 

    while True:
        best_receiver_need_metric = -float('inf') # Higher is better (e.g., -receiver_battery)
        chosen_sender_id = -1
        chosen_receiver_id = -1
        chosen_tx_power_W = 0.0 # Must be at least E_MIN_W to be valid

        for sender_node_obj in nodes:
            sender_id = sender_node_obj['id']
            if sender_id in nodes_ec_busy:
                continue

            # 1. Sender Condition: Must have enough energy to send at least E_MIN_W
            if sender_node_obj['battery'] < E_MIN_W:
                continue
            
            # 2. Find the qualified neighbor with the lowest energy for this sender
            current_sender_best_receiver_id = -1
            min_neighbor_energy = float('inf')

            if sender_id in potential_links:
                for neighbor_id in potential_links[sender_id]:
                    if neighbor_id in nodes_ec_busy:
                        continue
                    
                    neighbor_dev = get_device_by_id(neighbor_id, nodes, sinks)
                    if not neighbor_dev or neighbor_dev['is_sink']:
                        continue

                    # Receiver Condition: Energy below GAMMA_THRESHOLD * BATTERY_MAX_J
                    if neighbor_dev['battery'] < GAMMA_THRESHOLD * BATTERY_MAX_J:
                        if neighbor_dev['battery'] < min_neighbor_energy:
                            min_neighbor_energy = neighbor_dev['battery']
                            current_sender_best_receiver_id = neighbor_id
            
            # 3. If this sender found a qualified lowest-energy neighbor
            if current_sender_best_receiver_id != -1:
                # Determine heuristic metric for this (sender, chosen_receiver) pair
                # Prioritize pairs where the receiver is most needy (lowest energy)
                heuristic_metric_for_this_pair = -min_neighbor_energy 

                # Determine trial power for this pair
                sender_has_data = sum(sender_node_obj['queue_bits'].values()) > 1e-9
                if sender_has_data:
                    trial_power_W = E_MIN_W
                else: # No data, try to send more energy
                    trial_power_W = E_MAX_W
                
                # Ensure sender can afford this trial_power_W
                actual_tx_power_W_for_this_pair = min(trial_power_W, sender_node_obj['battery'])

                # Only consider this pair if it can send at least E_MIN_W and its metric is better
                if actual_tx_power_W_for_this_pair >= E_MIN_W and heuristic_metric_for_this_pair > best_receiver_need_metric:
                    best_receiver_need_metric = heuristic_metric_for_this_pair
                    chosen_sender_id = sender_id
                    chosen_receiver_id = current_sender_best_receiver_id
                    chosen_tx_power_W = actual_tx_power_W_for_this_pair
        
        # 4. If a best pair was chosen in this iteration of the while loop
        if chosen_sender_id != -1 and chosen_tx_power_W >= E_MIN_W : # Ensure valid power was chosen
            y_decisions[chosen_sender_id] = 1
            e_hat_decisions[chosen_sender_id] = chosen_tx_power_W
            
            nodes_ec_busy.add(chosen_sender_id)
            nodes_ec_busy.add(chosen_receiver_id) 
            # print(f"  UEC Heuristic: Node {chosen_sender_id} sends {chosen_tx_power_W:.4f}W to Node {chosen_receiver_id} (RxEnergy: {min_neighbor_energy:.2f})")
        else:
            # No more beneficial/possible unicast pairs found in this iteration
            break 

    # --- Phase 2: Data Transmission Decision (Identical to lyapunov_mec_scheduling's Phase 2) ---
    nodes_data_busy = set() 
    candidate_links_tuples = [] 

    for sender_id in range(NUM_NODES):
        sender_node = get_device_by_id(sender_id, nodes, sinks)
        if not sender_node or sender_id not in potential_links: continue

        e_hat_i = e_hat_decisions[sender_id] 

        if sender_node['battery'] < P_MIN_W + e_hat_i:
            continue

        for receiver_id in potential_links[sender_id]:
            receiver_dev = get_device_by_id(receiver_id, nodes, sinks)
            if not receiver_dev: continue

            e_hat_j = e_hat_decisions.get(receiver_id, 0.0) 
            channel_gain_ij = current_channel_gains.get((sender_id, receiver_id), 0.0)

            if channel_gain_ij <= 0: continue

            for sink_k_id in sender_node['queue_bits']: 
                p_required_min_W = calculate_required_min_power(channel_gain_ij)

                if p_required_min_W == float('inf'):
                    continue

                if sender_node['battery'] < p_required_min_W + e_hat_i:
                    continue
                
                f_potential_bits = calculate_potential_flow(sender_node, receiver_dev, sink_k_id, p_required_min_W, channel_gain_ij, e_hat_j)

                if f_potential_bits > 1e-9:
                    q_ik = sender_node['queue_bits'].get(sink_k_id, 0.0) 
                    q_jk = 0.0 
                    if not receiver_dev['is_sink']:
                        q_jk = receiver_dev['queue_bits'].get(sink_k_id, 0.0) 
                    is_final_hop = (receiver_id == sink_k_id)
                    W_lk = q_ik - q_jk + (V_CONTROL if is_final_hop else 0.0)

                    B_i = sender_node['energy_deficit_queue']
                    B_j = 0.0
                    receive_energy_cost_J = 0.0
                    if not receiver_dev['is_sink']:
                        B_j = receiver_dev['energy_deficit_queue']
                        receive_energy_cost_J = P_RCV_J_per_bit * f_potential_bits
                    
                    net_weight = (W_lk * f_potential_bits) - (B_i * p_required_min_W) - (B_j * receive_energy_cost_J)
                    candidate_links_tuples.append(((sender_id, receiver_id, sink_k_id), net_weight, f_potential_bits, p_required_min_W))

    candidate_links_tuples.sort(key=lambda x: x[1], reverse=True)

    for candidate in candidate_links_tuples:
        (i, j, k), weight, flow_bits, p_req_W = candidate 

        if weight <= 0:
            continue 

        sender_node = get_device_by_id(i, nodes, sinks)
        receiver_dev = get_device_by_id(j, nodes, sinks)

        sender_busy = i in nodes_data_busy
        receiver_busy = (not receiver_dev['is_sink']) and (j in nodes_data_busy)

        if not sender_busy and not receiver_busy:
            p_actual_W = p_req_W 
            e_hat_i = e_hat_decisions[i]
            e_hat_j = e_hat_decisions.get(j, 0.0)

            sender_energy_ok = sender_node['battery'] >= p_actual_W + e_hat_i
            receiver_energy_ok = True
            if not receiver_dev['is_sink']:
                 required_rcv_energy = P_RCV_J_per_bit * flow_bits
                 receiver_energy_ok = receiver_dev['battery'] >= required_rcv_energy + e_hat_j
            
            if sender_energy_ok and receiver_energy_ok and flow_bits > 1e-9: 
                link_key = (i, j, k)
                power_key = (i, j)
                x_decisions[link_key] = 1
                if power_key not in p_decisions:
                     p_decisions[power_key] = p_actual_W
                f_decisions[link_key] = flow_bits 

                nodes_data_busy.add(i)
                if not receiver_dev['is_sink']:
                    nodes_data_busy.add(j)

    actions = {
        'y': y_decisions,
        'e_hat': e_hat_decisions,
        'x': x_decisions,
        'p': p_decisions,
        'f': f_decisions
    }
    return actions


def update_node_states(nodes, sinks, actions, current_channel_gains, current_slot,
                       arrival_prob, packet_size_mean_bits, sinks_list): # Ensure packet_details is not passed
    """
    Updates battery levels, queues, and virtual queues for the next time slot (t+1).
    Args:
        nodes: List of node dictionaries (mutable state).
        sinks: List of sink dictionaries.
        actions: Dictionary of decisions made by the scheduler for slot t.
                 {'y':{id:0/1}, 'e_hat':{id:W}, 'x':{(i,j,k):1}, 'p':{(i,j):W}, 'f':{(i,j,k):bits}}
        current_channel_gains: Dict {(sender_id, receiver_id): inst_gain} for slot t.
        current_slot: The current time slot index (t).
        arrival_prob: Probability of data arrival per node per slot.
        packet_size_mean_bits: Mean packet size in bits.
        sinks_list: The list of sink dictionaries (needed for random choice).
    Returns:
        Tuple: (Amount of data delivered to sinks (bits), Dictionary of RF harvested energy per node {node_id: joules})
    """
    y_t = actions['y']
    e_hat_t = actions['e_hat']
    # x_t = actions['x'] # Not directly needed for state update, implied by f_t
    p_t = actions['p']
    f_t = actions['f']

    # --- Calculate energy consumption and harvesting for each node in slot t ---
    energy_consumed_tx_data = {node['id']: 0.0 for node in nodes} # p_hat_i^t
    energy_consumed_rx_data = {node['id']: 0.0 for node in nodes} # p_check_i^t
    energy_consumed_sensing = {node['id']: 0.0 for node in nodes} # s_i^t
    energy_consumed_tx_energy = {node['id']: e_hat_t.get(node['id'], 0.0) for node in nodes} # e_hat_i^t (already decided)
    energy_harvested_rf = {node['id']: 0.0 for node in nodes}       # e_check_i^t
    energy_harvested_env = {node['id']: 0.0 for node in nodes}      # h_i^t
    # global packet_id_counter, packet_details # REMOVED: No longer needed
    # data_arrived_at_node = {node['id']: {sink['id']: 0.0 for sink in sinks} for node in nodes} # a_ik^t - Now handled by adding packets
    # data_flow_in = {node['id']: {sink['id']: 0.0 for sink in sinks} for node in nodes} # REMOVED: No longer tracking individual packets
    # data_flow_out = {node['id']: {sink['id']: 0.0 for sink in sinks} for node in nodes} # REMOVED: No longer tracking individual packets
    # MODIFIED: Separate RF power sources
    total_rf_power_data_received = {node['id']: 0.0 for node in nodes}
    total_rf_power_energy_received = {node['id']: 0.0 for node in nodes}
    # total_rf_power_received = {node['id']: 0.0 for node in nodes} # REMOVED: Replaced by separate data/energy power
    data_delivered_to_sinks_bits = 0.0 # Keep track of total bits delivered for throughput calculation
    # newly_generated_packets = {node['id']: {sink['id']: [] for sink in sinks} for node in nodes} # REMOVED: No longer tracking individual packets
    arrived_bits_at_node = {node['id']: {sink['id']: 0.0 for sink in sinks} for node in nodes} # Store arrived bits directly

    # 1. Data Arrivals and Sensing Cost
    for node in nodes:
        # Generate new data?
        if random.random() < arrival_prob: # Use passed argument
            # Generate packet size (Exponential distribution)
            if packet_size_mean_bits > 0:
                lambda_rate = 1.0 / packet_size_mean_bits # Rate parameter lambda = 1 / mean
                packet_size_bits = np.random.exponential(scale=1.0/lambda_rate)
                packet_size_bits = max(1.0, packet_size_bits) # Ensure at least 1 bit
            else:
                packet_size_bits = 0 # Handle case where mean size is zero

            if packet_size_bits > 0:
                # Choose random destination sink
                dest_sink = random.choice(sinks_list) # Use passed argument
                dest_sink_id = dest_sink['id']

                # REMOVED: Packet ID generation and details storage

                # Store arrived bits directly
                arrived_bits_at_node[node['id']][dest_sink_id] += packet_size_bits

                # Calculate sensing cost (Eq. 1)
                energy_consumed_sensing[node['id']] += P_SENSE_J_per_bit * packet_size_bits

    # 2. Calculate Data Transmission Energy Cost (p_hat_i^t - Eq. 10a)
    # Sum power used by node i for all activated links originating from it
    for (i, j), power_W in p_t.items():
         if i < NUM_NODES: # Ensure sender is a node
             # Assuming unit time slot E=P*1s
             energy_consumed_tx_data[i] += power_W

    # 3. Process Data Transmission Decisions (f_t) and Calculate Reception Costs
    # MODIFIED: f_t now directly contains the number of bits transferred {(i, j, k): float_bits}
    # REMOVED: Packet ID tracking (packets_to_remove_from_sender, packets_to_add_to_receiver)
    # Track total bits transferred per link for queue_bits update
    bits_flow_out = {node['id']: {sink['id']: 0.0 for sink in sinks} for node in nodes}
    bits_flow_in = {node['id']: {sink['id']: 0.0 for sink in sinks} for node in nodes}

    for (i, j, k), bits_transferred in f_t.items():
        # Ensure bits_transferred is a valid positive number
        if not isinstance(bits_transferred, (int, float)) or bits_transferred <= 1e-9:
            continue

        # --- Process Sender (if node) ---
        if i < NUM_NODES:
            bits_flow_out[i][k] += bits_transferred
            # REMOVED: Packet ID list removal

        # --- Process Receiver ---
        receiver_dev = get_device_by_id(j, nodes, sinks)
        if receiver_dev:
            if not receiver_dev['is_sink']: # Receiver is a node
                bits_flow_in[j][k] += bits_transferred
                # REMOVED: Packet ID list addition
                # Calculate reception energy cost (p_check_j^t) - based on bits_transferred
                energy_consumed_rx_data[j] += P_RCV_J_per_bit * bits_transferred
            else: # Receiver is a sink
                data_delivered_to_sinks_bits += bits_transferred
                # REMOVED: Delay calculation logic

    # 4. Calculate Total RF Power Received at each potential receiver node (Eq. 9)
    # (Logic remains the same)
    for receiver_node in nodes:
        receiver_id = receiver_node['id']
        # Can only receive RF if not transmitting energy (y_t[receiver_id] == 0)
        if y_t.get(receiver_id, 0) == 0:
            # MODIFIED: Initialize separate power accumulators
            total_rf_power_data = 0.0
            total_rf_power_energy = 0.0
            # total_rf_power = 0.0 # REMOVED: Using separate accumulators
            # Sum power from neighbors transmitting data or energy
            # Need to iterate through all devices to find potential senders
            for sender_dev in all_devices:
                sender_id = sender_dev['id']
                if sender_id == receiver_id: continue # Skip self

                # Check if sender is a neighbor (pre-calculated in avg_channel_gains keys)
                if (sender_id, receiver_id) in avg_channel_gains:
                    inst_gain_ji = current_channel_gains.get((sender_id, receiver_id), 0.0)
                    if inst_gain_ji > 0:
                        # Power from data transmission originating from sender_id
                        # Need to check all links originating from sender_id ending at receiver_id
                        data_power_W = p_t.get((sender_id, receiver_id), 0.0)
                        # MODIFIED: Add to data power accumulator
                        total_rf_power_data += data_power_W * inst_gain_ji
                        # total_rf_power += data_power_W * inst_gain_ji # REMOVED

                        # Power from energy transmission
                        energy_power_W = e_hat_t.get(sender_id, 0.0) # Energy power from s
                        # MODIFIED: Add to energy power accumulator
                        total_rf_power_energy += energy_power_W * inst_gain_ji
                        # total_rf_power += energy_power_W * inst_gain_ji # REMOVED
            # MODIFIED: Store separated powers
            total_rf_power_data_received[receiver_id] = total_rf_power_data
            total_rf_power_energy_received[receiver_id] = total_rf_power_energy
            # total_rf_power_received[receiver_id] = total_rf_power # REMOVED

    # 5. Calculate RF Harvested Energy (e_check_i^t - Eq. 9a)
    for node in nodes:
        node_id = node['id']
        # Can only harvest RF if not transmitting energy
        if y_t.get(node_id, 0) == 0:
            # MODIFIED: Calculate effective power using eta_data = 0.1
            rf_data = total_rf_power_data_received.get(node_id, 0.0)
            rf_energy = total_rf_power_energy_received.get(node_id, 0.0)
            eta_data = 0.1 # Efficiency factor for data signal harvesting
            rf_effective_total = eta_data * rf_data + rf_energy
            energy_harvested_rf[node_id] = rf_harvesting(rf_effective_total)
            # energy_harvested_rf[node_id] = rf_harvesting(total_rf_power_received[node_id]) # REMOVED: Using effective power

    # 6. Calculate Environmental Harvested Energy (h_i^t)
    # TODO: Implement state transitions for env_eh_state here if needed
    for node in nodes:
        energy_harvested_env[node['id']] = environment_harvesting(node) # Uses current state for now

    # --- Update Node States for next slot (t+1) ---
    for node in nodes:
        node_id = node['id']

        # Calculate total energy consumed in slot t (Eq. 11)
        total_consumption_t = (energy_consumed_sensing[node_id] +
                               energy_consumed_tx_data[node_id] +
                               energy_consumed_rx_data[node_id] +
                               energy_consumed_tx_energy[node_id])

        # Ensure consumption doesn't exceed current battery (should be guaranteed by scheduler if checks are correct)
        # Apply consumption *before* adding harvested energy
        current_battery = node['battery']
        consumed_energy = min(total_consumption_t, current_battery)
        battery_after_consumption = current_battery - consumed_energy

        # Calculate actual energy gain in slot t (Eq. 12a, 12b)
        potential_energy_gain = energy_harvested_rf[node_id] + energy_harvested_env[node_id]
        # Energy cannot exceed battery capacity
        max_possible_charge = BATTERY_MAX_J - battery_after_consumption
        actual_energy_gain_t = max(0, min(potential_energy_gain, max_possible_charge)) # bar_e_i^t

        # Update Battery Level for t+1 (Eq. 12)
        node['battery'] = battery_after_consumption + actual_energy_gain_t
        # Clamp battery level to [0, b_max] just in case (due to float precision)
        node['battery'] = max(0, min(node['battery'], BATTERY_MAX_J))
        battery_t_plus_1 = node['battery'] # Store for virtual queue update

        # Update Data Queues for t+1 (Eq. 11 adapted)
        for sink_k_id in node['queue_bits']: # Iterate through sinks relevant to this node

            # REMOVED: Update Packet ID List (node['queues'])

            # --- Update Bit Count (node['queue_bits']) - Based on flows ---
            q_ik_t = node['queue_bits'].get(sink_k_id, 0.0) # Current bits
            # Bits from new arrivals (use arrived_bits_at_node calculated earlier)
            arrived_bits = arrived_bits_at_node[node_id].get(sink_k_id, 0.0)
            # REMOVED: Reference to packet_details in arrived_bits calculation (was incorrect anyway)
            # Bits received from other nodes
            in_flow_bits = bits_flow_in[node_id].get(sink_k_id, 0.0)
            # Bits sent to other nodes/sink
            out_flow_bits = bits_flow_out[node_id].get(sink_k_id, 0.0)

            # Update formula: q(t+1) = q(t) + arrived + received - sent
            q_ik_t_plus_1 = q_ik_t + arrived_bits + in_flow_bits - out_flow_bits

            # Apply constraints (non-negative, max capacity)
            node['queue_bits'][sink_k_id] = max(0.0, min(q_ik_t_plus_1, QUEUE_MAX_bits))

            # REMOVED: Sanity Check/Cleanup comparing queue_bits to packet list


        # Update Energy Deficit Queue for t+1 (Eq. 14)
        B_i_t = node['energy_deficit_queue']
        node['energy_deficit_queue'] = max(0, B_i_t + DELTA_I - battery_t_plus_1)

        # Update Environment EH State for t+1 using Transition Matrix P
        current_state = node['env_eh_state']
        probabilities = ENV_EH_TRANSITION_P[current_state, :]
        # Choose next state based on probabilities
        # Need state indices [0, 1, 2, 3] corresponding to the rows/cols of P
        possible_next_states = np.arange(ENV_EH_STATES)
        node['env_eh_state'] = np.random.choice(possible_next_states, p=probabilities)


    return data_delivered_to_sinks_bits, energy_harvested_rf # Return delivered data and harvested RF energy


def run_single_simulation(scheduling_func):
    """
    Runs one full simulation from t=0 to T using the specified scheduling function.
    Args:
        scheduling_func: The scheduling function to use (e.g., lyapunov_mec_scheduling).
    Returns:
        A dictionary containing simulation results.
    """
    global nodes, sinks, all_devices, potential_links, avg_channel_gains # Ensure globals are declared

    # --- Clear per-run state ---
    # Initialize network for each run to ensure independence
    # Need to reset node states completely, including queues etc.
    initialize_network() # This re-initializes nodes, sinks, links, gains

    # --- Data logging ---
    total_delivered_data_bits = 0.0
    # Lists to store per-timeslot metrics
    log_timeslot = list(range(TOTAL_TIME_SLOTS))
    log_avg_battery = []
    log_total_queue_bits = []
    log_avg_energy_deficit = []
    log_delivered_bits_per_slot = []
    # log_avg_delay_per_slot = [] # REMOVED: Delay logging

    start_time = time.time()

    for t in range(TOTAL_TIME_SLOTS):
        if t % 1000 == 0: # Print progress less frequently
             elapsed = time.time() - start_time
             print(f"  Slot {t}/{TOTAL_TIME_SLOTS} (Elapsed: {elapsed:.2f}s)")

        # 1. Get current state (queues, battery levels) - already in 'nodes' list

        # 2. Get current instantaneous channel conditions
        current_channel_gains = {}
        for (sender_id, receiver_id), avg_gain in avg_channel_gains.items():
            if avg_gain > 0:
                inst_gain = get_rician_channel_gain(avg_gain)
                current_channel_gains[(sender_id, receiver_id)] = inst_gain
            else:
                current_channel_gains[(sender_id, receiver_id)] = 0

        # 3. Run the specified scheduling algorithm
        # Pass necessary parameters including the local packet_details and constants
        # Need to check which parameters each specific scheduling function requires
        # Lyapunov functions use global packet_details implicitly
        if scheduling_func in [lyapunov_mec_scheduling, lyapunov_noec_scheduling, lyapunov_uec_scheduling]: # Added UEC
             actions = scheduling_func(t, nodes, sinks, potential_links, current_channel_gains)
        # Comparison algorithms use global packet_details implicitly but need constants
        elif scheduling_func in [gmw_scheduling, rand_scheduling]:
             actions = scheduling_func(t, nodes, sinks, potential_links, current_channel_gains,
                                       NOISE_POWER_W, BANDWIDTH)
        elif scheduling_func == eag_scheduling:
             actions = scheduling_func(t, nodes, sinks, potential_links, current_channel_gains,
                                       NOISE_POWER_W, BANDWIDTH, BATTERY_MAX_J)
        else:
             print(f"Warning: Unknown scheduling function {scheduling_func}. Skipping.")
             actions = {'y': {}, 'e_hat': {}, 'x': {}, 'p': {}, 'f': {}} # Empty actions


        # 4. Update node states based on actions and harvesting
        # update_node_states uses global packet_details implicitly
        delivered_this_slot = update_node_states(nodes, sinks, actions, current_channel_gains, t,
                                                 ARRIVAL_PROB, PACKET_SIZE_MEAN_bits, sinks)
        # Unpack the tuple returned by update_node_states
        delivered_bits_this_slot, _ = delivered_this_slot # We only need the first value here

        # --- Log metrics for this timeslot ---
        total_delivered_data_bits += delivered_bits_this_slot
        log_delivered_bits_per_slot.append(delivered_bits_this_slot)

        current_batteries = [n['battery'] for n in nodes]
        # Corrected: Sum the bit counts from 'queue_bits', not the packet ID lists from 'queues'
        current_total_queue_bits = sum(sum(n['queue_bits'].values()) for n in nodes)
        current_energy_deficits = [n['energy_deficit_queue'] for n in nodes]

        # REMOVED: Delay calculation and logging

        log_avg_battery.append(np.mean(current_batteries) if current_batteries else 0)
        # Log the total bits calculated above
        log_total_queue_bits.append(current_total_queue_bits)
        log_avg_energy_deficit.append(np.mean(current_energy_deficits) if current_energy_deficits else 0)


    # --- Calculate final metrics for this run ---
    # Ensure TOTAL_TIME_SLOTS is not zero to avoid division by zero
    avg_throughput_bps = total_delivered_data_bits / TOTAL_TIME_SLOTS if TOTAL_TIME_SLOTS > 0 else 0.0
    avg_throughput_mbps = avg_throughput_bps / 1e6 # Convert to Mbps

    # --- Calculate time-averaged metrics for this run ---
    time_avg_battery = np.mean(log_avg_battery) if log_avg_battery else 0
    time_avg_queue_mbit = np.mean([q / 1e6 for q in log_total_queue_bits]) if log_total_queue_bits else 0
    time_avg_energy_deficit = np.mean(log_avg_energy_deficit) if log_avg_energy_deficit else 0

    # REMOVED: Average delay calculation for the run

    # Return final metrics, timeseries logs, and time-averaged metrics
    results = {
        # Final metrics
        'final_avg_throughput_mbps': avg_throughput_mbps, # Renamed for clarity
        'final_total_delivered_mbits': total_delivered_data_bits / 1e6, # Renamed for clarity
        # 'final_avg_packet_delay': avg_delay_this_run, # REMOVED: Delay metric
        # Time-averaged metrics for box plots
        'time_avg_battery': time_avg_battery,
        'time_avg_queue_mbit': time_avg_queue_mbit,
        'time_avg_energy_deficit': time_avg_energy_deficit,
        # Timeseries logs (optional for box plots, but keep for potential line plots)
        'log_timeslot': log_timeslot,
        'log_avg_battery': log_avg_battery,
        'log_total_queue_mbit': [q / 1e6 for q in log_total_queue_bits],
        'log_avg_energy_deficit': log_avg_energy_deficit,
        'log_delivered_mbps': [d / 1e6 for d in log_delivered_bits_per_slot],
        # 'log_avg_delay_per_slot': log_avg_delay_per_slot # REMOVED: Delay log
        # We don't store the full list of delays per run to save memory, only the average
    }
    # print(f"  Run finished. Avg Throughput: {avg_throughput_mbps:.4f} Mbps, Avg Delay: {avg_delay_this_run:.2f} slots") # REMOVED: Delay from print
    print(f"  Run finished. Avg Throughput: {avg_throughput_mbps:.4f} Mbps")
    return results

# --- Result Processing and Plotting ---
import matplotlib.pyplot as plt

def process_results(all_run_results):
    """Aggregates results from multiple simulation runs."""
    if not all_run_results:
        return {}

    num_runs = len(all_run_results)
    # Ensure num_slots is determined safely, even if a run failed or returned early
    num_slots = 0
    if all_run_results and 'log_timeslot' in all_run_results[0]:
         num_slots = len(all_run_results[0]['log_timeslot'])
    if num_slots == 0:
        print("Warning: Could not determine number of slots from results.")
        return {} # Cannot process if timeslot info is missing

    processed = {}

    # --- Aggregate metrics for Box Plots ---
    # Collect the value of these metrics from each individual run
    boxplot_metrics_keys = [
        'final_avg_throughput_mbps', # Final throughput per run
        'time_avg_battery',
        'time_avg_queue_mbit',
        'time_avg_energy_deficit',
        # 'final_avg_packet_delay' # REMOVED: Delay metric
    ]
    for key in boxplot_metrics_keys:
        # Get the value of 'key' from each run's result dictionary
        distribution = [run_result.get(key, float('nan')) for run_result in all_run_results]
        processed[f'dist_{key}'] = distribution # Store the list of values (distribution)

    # --- Aggregate metrics for Line Plots (Mean/CI over time) ---
    # Keep this section if you might still want line plots
    # REMOVED: 'log_avg_delay_per_slot' from keys
    timeseries_keys = ['log_avg_battery', 'log_total_queue_mbit', 'log_avg_energy_deficit', 'log_delivered_mbps']
    for key in timeseries_keys:
        # Stack timeseries from all runs into a 2D array (runs x slots)
        # Filter results to ensure consistent length before stacking
        # Filter results to ensure consistent length before stacking
        valid_series = [r.get(key) for r in all_run_results if len(r.get(key, [])) == num_slots]
        if len(valid_series) == num_runs: # Ensure all runs had the expected length
            all_series = np.array(valid_series)
            processed[f'mean_{key}'] = np.mean(all_series, axis=0)
            processed[f'std_{key}'] = np.std(all_series, axis=0)
            if num_runs > 1:
                 processed[f'ci95_{key}'] = 1.96 * (processed[f'std_{key}'] / np.sqrt(num_runs))
            else:
                 processed[f'ci95_{key}'] = np.zeros_like(processed[f'mean_{key}']) # CI is zero for single run
        else:
             print(f"Warning: Inconsistent lengths for timeseries key '{key}'. Found {len(valid_series)} valid runs out of {num_runs}. Skipping aggregation.")


    processed['log_timeslot'] = all_run_results[0].get('log_timeslot', []) # Get timeslots from first run

    return processed

def plot_comparison_results(all_processed_data):
    """Generates comparison plots based on aggregated simulation results for multiple algorithms."""
    if not all_processed_data:
        print("No processed data to plot.")
        return

    # Get timeslots from the first algorithm's data (assuming all have same length)
    first_algo_name = list(all_processed_data.keys())[0]
    timeslots = all_processed_data[first_algo_name].get('log_timeslot', [])
    if not timeslots:
        print("No timeslot data found for plotting.")
        return

    # Change figure size and layout for 3x2 grid
    plt.figure(figsize=(12, 11)) # Increased height for 3 rows
    # Define colors or let matplotlib cycle automatically
    # colors = plt.cm.viridis(np.linspace(0, 1, len(all_processed_data)))

    # Plot 1: Average Battery Level over Time (Position 1)
    plt.subplot(3, 2, 1) # Changed subplot position
    for algo_name, processed_data in all_processed_data.items():
        mean_battery = processed_data.get('mean_log_avg_battery', [])
        ci_battery = processed_data.get('ci95_log_avg_battery', []) # Optional CI
        if len(mean_battery) == len(timeslots) and len(ci_battery) == len(timeslots):
            plt.plot(timeslots, mean_battery, label=algo_name) # Use algo_name as label
            # Optional: Plot CI
            plt.fill_between(timeslots, mean_battery - ci_battery, mean_battery + ci_battery, alpha=0.2)
    plt.axhline(y=DELTA_I, color='r', linestyle='--', linewidth=1, label=f'Threshold $\\delta_i$ ({DELTA_I:.1f} J)')
    plt.xlabel("Time Slot")
    plt.ylabel("Average Battery (J)")
    plt.title("Average Battery Level Comparison")
    plt.ylim(bottom=0, top=BATTERY_MAX_J)
    plt.grid(True)
    plt.legend()

    # Plot 2: Total Queue Size over Time (Position 2)
    plt.subplot(3, 2, 2) # Changed subplot position
    for algo_name, processed_data in all_processed_data.items():
        mean_queue = processed_data.get('mean_log_total_queue_mbit', [])
        ci_queue = processed_data.get('ci95_log_total_queue_mbit', []) # Optional CI
        if len(mean_queue) == len(timeslots) and len(ci_queue) == len(timeslots):
            plt.plot(timeslots, mean_queue, label=algo_name)
            # Optional: Plot CI
            plt.fill_between(timeslots, mean_queue - ci_queue, mean_queue + ci_queue, alpha=0.2)
    plt.xlabel("Time Slot")
    plt.ylabel("Total Queue Size (Mbit)")
    plt.title("Total Network Queue Size Comparison")
    plt.grid(True)
    plt.legend()

    # Plot 3: Average Energy Deficit Queue over Time (Position 3)
    plt.subplot(3, 2, 3) # Changed subplot position
    for algo_name, processed_data in all_processed_data.items():
        mean_deficit = processed_data.get('mean_log_avg_energy_deficit', [])
        ci_deficit = processed_data.get('ci95_log_avg_energy_deficit', []) # Optional CI
        if len(mean_deficit) == len(timeslots) and len(ci_deficit) == len(timeslots):
            plt.plot(timeslots, mean_deficit, label=algo_name)
            # Optional: Plot CI
            plt.fill_between(timeslots, mean_deficit - ci_deficit, mean_deficit + ci_deficit, alpha=0.2)
    plt.xlabel("Time Slot")
    plt.ylabel("Average Energy Deficit")
    plt.title("Average Energy Deficit Queue Comparison")
    plt.grid(True)
    plt.legend()

    # Plot 4: Average Instantaneous Throughput (Smoothed) (Position 4)
    plt.subplot(3, 2, 4) # Changed subplot position
    window_size = 100 # Smoothing window
    for algo_name, processed_data in all_processed_data.items():
        mean_delivered = processed_data.get('mean_log_delivered_mbps', [])
        if len(mean_delivered) >= window_size:
            actual_window = min(window_size, len(mean_delivered))
            smoothed_throughput = np.convolve(mean_delivered, np.ones(actual_window)/actual_window, mode='valid')
            smoothed_timeslots = timeslots[actual_window-1:]
            plt.plot(smoothed_timeslots, smoothed_throughput, label=algo_name)
        elif len(mean_delivered) == len(timeslots): # Plot raw if too short to smooth
             plt.plot(timeslots, mean_delivered, label=f"{algo_name} (Raw)")

    plt.xlabel("Time Slot")
    plt.ylabel("Avg Delivered Rate (Mbps)")
    plt.title(f"Average Instantaneous Throughput Comparison (Smoothed {window_size} slots)")
    plt.grid(True)
    plt.legend()

    # REMOVED: Plot 5 (Average Packet Delay)

    # Adjust layout to prevent overlapping titles/labels
    # Adjust subplot parameters if needed now that there are fewer plots
    plt.subplots_adjust(hspace=0.4, wspace=0.3) # Example adjustment
    # plt.tight_layout(pad=2.0) # tight_layout might conflict with subplots_adjust

def plot_comparison_boxplots(all_processed_data):
    """Generates comparison box plots based on aggregated simulation results for multiple algorithms."""
    if not all_processed_data:
        print("No processed data to plot.")
        return

    algo_names = list(all_processed_data.keys())
    num_algos = len(algo_names)

    # Metrics to plot (using the 'dist_' prefix from process_results)
    # Use the correct key for final throughput distribution
    metrics_to_plot = {
        'dist_final_avg_throughput_mbps': 'Final Average Throughput (Mbps)',
        'dist_time_avg_battery': 'Time-Averaged Battery (J)',
        'dist_time_avg_queue_mbit': 'Time-Averaged Queue Size (Mbit)',
        'dist_time_avg_energy_deficit': 'Time-Averaged Energy Deficit',
        # 'dist_final_avg_packet_delay': 'Average Packet Delay (Slots)' # REMOVED: Delay metric
    }

    num_metrics = len(metrics_to_plot)
    # Adjust figure layout: maybe 2 columns if many metrics? Or keep 1 column.
    # Let's try 2 columns if more than 2 metrics.
    ncols = 2 if num_metrics > 2 else 1
    nrows = (num_metrics + ncols - 1) // ncols
    plt.figure(figsize=(max(6 * ncols, num_algos * 1.5 * ncols), 4 * nrows)) # Adjust figure size

    plot_index = 1
    for metric_key, plot_title in metrics_to_plot.items():
        plt.subplot(nrows, ncols, plot_index)
        
        # Prepare data for boxplot: list of lists/arrays, one for each algorithm
        data_to_plot = []
        valid_algo_names = [] # Keep track of algos with valid data for this metric
        for algo_name in algo_names:
            if algo_name in all_processed_data and metric_key in all_processed_data[algo_name]:
                # Get the distribution data (list of values from each run)
                dist_data = all_processed_data[algo_name][metric_key]
                # Filter out potential NaN values if any run failed to produce this metric
                valid_dist_data = [d for d in dist_data if not np.isnan(d)]
                if valid_dist_data:
                    data_to_plot.append(valid_dist_data)
                    valid_algo_names.append(algo_name)
                else:
                     print(f"Warning: No valid data for metric '{metric_key}' in algorithm '{algo_name}'. Skipping.")
            else:
                print(f"Warning: Metric '{metric_key}' not found for algorithm '{algo_name}'. Skipping.")

        if data_to_plot: # Only plot if there's data
            bp = plt.boxplot(data_to_plot, patch_artist=True, labels=valid_algo_names, showfliers=False) # Disable outliers for cleaner look initially

            # Optional: Customize box colors
            colors = plt.cm.get_cmap('viridis', len(data_to_plot)) # Use a colormap
            for i, patch in enumerate(bp['boxes']):
                patch.set_facecolor(colors(i))
                patch.set_alpha(0.7) # Add transparency

            plt.title(plot_title + " Comparison")
            plt.ylabel("Value")
            plt.grid(True, axis='y') # Grid lines for y-axis only
            # Rotate x-axis labels if too many algorithms
            if num_algos > 4: # Rotate if more than 4 algos
                 plt.xticks(rotation=30, ha='right')

        plot_index += 1

    plt.tight_layout(pad=2.0) # Add padding
    plt.show()


# --- Main Simulation Loop ---

if __name__ == "__main__":
    # Define algorithms to run
    algorithms_to_run = {
        "Lyapunov-MEC": lyapunov_mec_scheduling,
        "Lyapunov-NoEC": lyapunov_noec_scheduling,
        "Lyapunov-UEC": lyapunov_uec_scheduling, # Added UEC
        "GMW": gmw_scheduling,
        "EAG": eag_scheduling,
        "RAND": rand_scheduling
    }

    # Dictionary to store results for each algorithm
    all_algorithm_results = {name: [] for name in algorithms_to_run}
    processed_algorithm_data = {}

    overall_start_time = time.time()

    # Loop through each algorithm
    for algo_name, scheduling_func in algorithms_to_run.items():
        print(f"\n--- Running Algorithm: {algo_name} ---")
        algo_start_time = time.time()
        current_algo_results = [] # Store results for this algorithm's runs

        # Run multiple simulations for the current algorithm
        for run in range(NUM_SIM_RUNS):
            print(f"  Starting Run {run+1}/{NUM_SIM_RUNS} for {algo_name}...")
            run_start_time = time.time()
            # Pass the specific scheduling function to run_single_simulation
            results = run_single_simulation(scheduling_func)
            current_algo_results.append(results)
            run_end_time = time.time()
            print(f"  Finished Run {run+1}. Time: {run_end_time - run_start_time:.2f}s")

        # Store all results for this algorithm
        all_algorithm_results[algo_name] = current_algo_results
        algo_end_time = time.time()
        print(f"--- Finished {algo_name}. Total Time: {algo_end_time - algo_start_time:.2f}s ---")

    overall_end_time = time.time()
    print(f"\nTotal Simulation Time for all algorithms: {overall_end_time - overall_start_time:.2f}s")

    # --- Data Aggregation and Plotting ---
    print("\n--- Processing Results ---")
    for algo_name, results_list in all_algorithm_results.items():
        print(f"Processing results for {algo_name}...") # Corrected indentation (4 spaces)
        processed_data = process_results(results_list)
        processed_algorithm_data[algo_name] = processed_data

        # Example: Print aggregated final metrics for each algorithm
        if processed_data: # 4 spaces
            print(f"\n--- Aggregated Results for {algo_name} ---") # Corrected indentation (8 spaces)
            mean_tp = processed_data.get('mean_avg_throughput_mbps', float('nan')) # Corrected indentation (8 spaces)
            # Use the correct keys based on process_results
            # Example: Accessing the distribution for final throughput
            throughput_dist = processed_data.get('dist_final_avg_throughput_mbps', [])
            # delay_dist = processed_data.get('dist_final_avg_packet_delay', []) # REMOVED

            if throughput_dist: # Check if data exists
                 mean_tp = np.mean(throughput_dist)
                 std_tp = np.std(throughput_dist)
                 ci95_tp = 1.96 * (std_tp / np.sqrt(len(throughput_dist))) if len(throughput_dist) > 1 else 0
                 print(f"  Average Throughput: {mean_tp:.4f} +/- {ci95_tp:.4f} Mbps (95% CI)")
            else:
                 print("  Average Throughput: N/A")

            # REMOVED: Delay statistics printing

        else: # 4 spaces
            print(f"  No processed data available for {algo_name}.") # Corrected indentation (8 spaces)

    # --- ADD CODE HERE TO SAVE INSTANTANEOUS THROUGHPUT DATA ---
    instantaneous_throughput_data_to_save = {}
    timeslot_data_captured = False # Flag to ensure timeslots are saved only once

    for algo_name, data in processed_algorithm_data.items():
        # Capture timeslots from the first algorithm that has it
        if not timeslot_data_captured and 'log_timeslot' in data and data['log_timeslot']:
            instantaneous_throughput_data_to_save['timeslot'] = data['log_timeslot']
            timeslot_data_captured = True
        
        # Save mean instantaneous throughput for the algorithm
        if 'mean_log_delivered_mbps' in data and data['mean_log_delivered_mbps'] is not None:
            # Convert numpy array to list for JSON serialization if applicable
            if hasattr(data['mean_log_delivered_mbps'], 'tolist'):
                instantaneous_throughput_data_to_save[algo_name] = data['mean_log_delivered_mbps'].tolist()
            elif isinstance(data['mean_log_delivered_mbps'], list):
                instantaneous_throughput_data_to_save[algo_name] = data['mean_log_delivered_mbps']
            else:
                # Log a warning if the data is not in an expected format or is None
                print(f"Warning: 'mean_log_delivered_mbps' for algorithm '{algo_name}' is not a list or numpy array, or is None. Skipping this algorithm for data saving.")

    # Proceed to save if data was collected and timeslots are present
    if instantaneous_throughput_data_to_save and 'timeslot' in instantaneous_throughput_data_to_save and len(instantaneous_throughput_data_to_save) > 1: # Ensure more than just timeslots
        try:
            with open("mean_instantaneous_throughput.json", 'w') as f_json:
                json.dump(instantaneous_throughput_data_to_save, f_json, indent=4)
            print("\nSuccessfully saved mean instantaneous throughput data to mean_instantaneous_throughput.json")
        except Exception as e_json_save:
            print(f"\nError saving instantaneous throughput data: {e_json_save}")
    else:
        print("\nWarning: No mean instantaneous throughput data was prepared for saving, timeslots were missing, or no algorithm data found.")
    # --- END OF ADDED CODE ---

    # --- Generate Comparison Plots ---
    # TODO: Modify plot_results or create plot_comparison_results
    #       to accept processed_algorithm_data and plot multiple algorithms.
    # Example call (assuming plot_results is modified):
    # plot_results(processed_algorithm_data) # Pass the dictionary of processed data

    # --- Generate Comparison Plots (Line plots and Box plots) ---
    print("\nGenerating comparison line plots...")
    # Check if data exists before plotting
    if processed_algorithm_data:
        plot_comparison_results(processed_algorithm_data) # Keep the line plots
    else:
        print("Skipping line plots: No processed data.")

    print("\nGenerating comparison box plots...")
    # Check if data exists before plotting
    if processed_algorithm_data:
        plot_comparison_boxplots(processed_algorithm_data) # Add the box plots
    else:
        print("Skipping box plots: No processed data.")


    print("\nSimulation finished.") # 4 spaces
