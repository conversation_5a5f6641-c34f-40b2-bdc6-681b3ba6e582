# comparison_algorithms.py
# Implements GMW, EAG, and RAND scheduling algorithms for comparison.

import numpy as np
import random
import math

# --- Necessary Constants (Copied from simulation.py for standalone execution) ---
# Note: Ideally, constants would be managed in a shared config file or module.
# Copying them here for simplicity based on user feedback.

# Communication Parameters
P_MIN_dBm = 5.0
P_MIN_W = 10**(P_MIN_dBm / 10) / 1000 # p_min in Watts

# Energy Consumption
P_RCV_J_per_Mbit = 50e-6 # p_rcv (Joules per Mbit)
P_RCV_J_per_bit = P_RCV_J_per_Mbit / 1e6

# Queue Capacity
QUEUE_MAX_Mbit = 50.0 # q_max (Mbit)
QUEUE_MAX_bits = QUEUE_MAX_Mbit * 1e6 # q_max (bits)

# --- Helper Functions (Copied from simulation.py) ---

def get_device_by_id(device_id, nodes, sinks):
    """Helper to find a node or sink by its ID."""
    for node in nodes:
        if node['id'] == device_id:
            return node
    for sink in sinks:
        if sink['id'] == device_id:
            return sink
    return None

# Need necessary functions called by calculate_potential_flow
def calculate_snr(power_tx_W, instantaneous_channel_gain, noise_power_W):
    """Calculates SNR based on transmit power and instantaneous channel gain."""
    if instantaneous_channel_gain <= 0 or noise_power_W <= 0:
        return 0
    snr = (power_tx_W * instantaneous_channel_gain) / noise_power_W
    return max(0, snr) # Ensure SNR is non-negative

def calculate_data_rate_bits_per_slot(snr, bandwidth):
    """Calculates data rate in bits per slot using Shannon capacity."""
    if snr <= 0:
        return 0
    # Assuming unit time slot (1 second), Rate (bps) = Capacity
    # Use math.log2 for base 2 logarithm
    try:
        # Ensure argument to log2 is strictly positive
        rate = bandwidth * math.log2(1 + max(snr, 1e-12))
    except ValueError: # Handle potential domain error if 1+snr is negative (shouldn't happen with max)
        rate = 0
    return rate

def calculate_potential_flow(sender_node, receiver_dev, sink_k_id, power_tx_W, channel_gain_ij, e_hat_j,
                             noise_power_W, bandwidth): # Added noise_power_W, bandwidth
    """
    Calculates the potential data flow f_lk^t based on constraints.
    Args:
        sender_node: Dictionary of the sending node.
        receiver_dev: Dictionary of the receiving device (node or sink).
        sink_k_id: ID of the final destination sink.
        power_tx_W: Transmit power used.
        channel_gain_ij: Instantaneous channel gain between sender and receiver.
        e_hat_j: Energy cooperation transmission power of the receiver (0 if not transmitting).
        noise_power_W: Noise power in Watts.
        bandwidth: Channel bandwidth in Hz.
    Returns:
        Potential data flow in bits.
    """
    # 1. Calculate Link Capacity (Rate) r_l^t (Eq. 7)
    snr = calculate_snr(power_tx_W, channel_gain_ij, noise_power_W) # Pass noise power
    rate_bits = calculate_data_rate_bits_per_slot(snr, bandwidth) # Pass bandwidth

    # 2. Sender Queue Constraint f_lk^t <= q_ik^t (Eq. 8b) - Use queue_bits
    sender_queue_bits = sender_node['queue_bits'].get(sink_k_id, 0.0)

    # 3. Receiver Constraints (only apply if receiver is a node, not a sink)
    receiver_energy_constraint_bits = float('inf')
    receiver_buffer_constraint_bits = float('inf')

    if not receiver_dev['is_sink']:
        # Receiver Energy Constraint f_lk^t <= (b_j^t - e_hat_j) / p_rcv (Eq. 8c, adapted)
        available_energy_for_reception = max(0, receiver_dev['battery'] - e_hat_j)
        if P_RCV_J_per_bit > 1e-12:
             receiver_energy_constraint_bits = math.floor(available_energy_for_reception / P_RCV_J_per_bit)
        else:
              receiver_energy_constraint_bits = float('inf')

        # Receiver Buffer Constraint f_lk^t <= q_max - sum(q_jk') (Eq. 8d)
        current_total_queue_j = sum(receiver_dev['queue_bits'].values())
        receiver_buffer_constraint_bits = max(0, QUEUE_MAX_bits - current_total_queue_j)

    # Determine the actual flow based on the minimum of all constraints
    potential_flow_bits = min(rate_bits,
                              sender_queue_bits,
                              receiver_energy_constraint_bits,
                              receiver_buffer_constraint_bits)

    return max(0, potential_flow_bits)


# Need access to global packet_details
# This assumes simulation.py defines packet_details globally
# It's generally better practice to pass it, but reverting based on user feedback
# from simulation import packet_details # Moved inside functions to avoid circular import

# --- GMW Scheduling Algorithm ---
def gmw_scheduling(current_slot, nodes, sinks, potential_links, current_channel_gains,
                   noise_power_W, bandwidth): # packet_details removed from args
    """
    Performs Greedy-MaxWeight (GMW) scheduling.
    Selects links greedily based on queue difference weight W_lk = q_ik - q_jk,
    subject to basic energy and half-duplex constraints. No energy cooperation.
    Args:
        current_slot: The current time slot index (t).
        nodes: List of node dictionaries (mutable state).
        sinks: List of sink dictionaries.
        potential_links: Dict {sender_id: [receiver_ids]}.
        current_channel_gains: Dict {(sender_id, receiver_id): inst_gain}.
        packet_details: Dict {packet_id: details}. Needed for packet selection.
        noise_power_W: Noise power in Watts.
        bandwidth: Channel bandwidth in Hz.
    Returns:
        A dictionary containing the decisions for this timeslot (y and e_hat are always 0):
        {
            'y': {node_id: 0},                   # Energy Tx state (always 0)
            'e_hat': {node_id: 0.0},             # Energy Tx power (always 0)
            'x': {(i, j, k): 0 or 1},            # Link activation for sink k
            'p': {(i, j): power_W},              # Data Tx power
            'f': {(i, j, k): float_bits_value}   # Data flow (BITS, not packet IDs)
        }
    """
    # Initialize decisions - No Energy Cooperation
    y_decisions = {node['id']: 0 for node in nodes}
    e_hat_decisions = {node['id']: 0.0 for node in nodes} # Always 0 for GMW
    x_decisions = {}
    p_decisions = {}
    f_decisions = {}

    nodes_data_busy = set()
    candidate_links_tuples = [] # List of ((i, j, k), Weight_Value, f_potential_bits)

    # 1. Calculate Weight W_lk = q_ik - q_jk for all potential links
    num_nodes = len(nodes) # Get number of nodes dynamically
    for sender_id in range(num_nodes): # Iterate up to num_nodes
        sender_node = get_device_by_id(sender_id, nodes, sinks)
        if not sender_node or sender_id not in potential_links: continue

        # Basic energy check: Can sender afford p_min?
        if sender_node['battery'] < P_MIN_W:
            continue

        for receiver_id in potential_links[sender_id]:
            receiver_dev = get_device_by_id(receiver_id, nodes, sinks)
            if not receiver_dev: continue

            channel_gain_ij = current_channel_gains.get((sender_id, receiver_id), 0.0)
            if channel_gain_ij <= 0: continue

            for sink_k_id in sender_node['queue_bits']: # Iterate using queue_bits keys
                # REMOVED: Check for non-empty packet list (sender_node['queues'])
                # Now rely on f_potential_bits > 0 check below
                p_trial_W = P_MIN_W # Use p_min for evaluation

                # Calculate potential flow (needed for receiver energy check and packet selection)
                    # GMW doesn't use e_hat, so pass 0.0
                f_potential_bits = calculate_potential_flow(sender_node, receiver_dev, sink_k_id, p_trial_W, channel_gain_ij, e_hat_j=0.0,
                                                                noise_power_W=noise_power_W, bandwidth=bandwidth) # Pass necessary params

                if f_potential_bits > 1e-9:
                    # Basic receiver energy check (if receiver is a node)
                    receiver_energy_ok = True
                    if not receiver_dev['is_sink']:
                        required_rcv_energy = P_RCV_J_per_bit * f_potential_bits
                        receiver_energy_ok = receiver_dev['battery'] >= required_rcv_energy

                    if receiver_energy_ok:
                        # Calculate GMW Link Weight W_lk = q_ik - q_jk
                        q_ik = sender_node['queue_bits'].get(sink_k_id, 0.0)
                        q_jk = 0.0
                        if not receiver_dev['is_sink']:
                            q_jk = receiver_dev['queue_bits'].get(sink_k_id, 0.0)
                        # GMW weight doesn't include the V term
                        gmw_weight = q_ik - q_jk

                        # Only consider links with positive weight
                        if gmw_weight > 0:
                            candidate_links_tuples.append(((sender_id, receiver_id, sink_k_id), gmw_weight, f_potential_bits))

    # 2. Sort candidates by GMW Weight
    candidate_links_tuples.sort(key=lambda x: x[1], reverse=True)

    # 3. Iteratively select links based on max weight and constraints
    for candidate in candidate_links_tuples:
        (i, j, k), weight, flow_bits = candidate # weight is gmw_weight

        sender_node = get_device_by_id(i, nodes, sinks)
        receiver_dev = get_device_by_id(j, nodes, sinks)

        # Check half-duplex constraint
        sender_busy = i in nodes_data_busy
        receiver_busy = (not receiver_dev['is_sink']) and (j in nodes_data_busy)

        if not sender_busy and not receiver_busy:
            p_actual_W = P_MIN_W

            # Final energy checks (re-check just before activation)
            sender_energy_ok = sender_node['battery'] >= p_actual_W
            receiver_energy_ok = True
            if not receiver_dev['is_sink']:
                 required_rcv_energy = P_RCV_J_per_bit * flow_bits
                 receiver_energy_ok = receiver_dev['battery'] >= required_rcv_energy

            if sender_energy_ok and receiver_energy_ok and flow_bits > 1e-9:
                # --- Packet selection logic removed - Using flow_bits directly ---

                # --- Store flow_bits directly for GMW/EAG/RAND ---
                link_key = (i, j, k)
                power_key = (i, j)
                x_decisions[link_key] = 1
                if power_key not in p_decisions:
                    p_decisions[power_key] = p_actual_W
                # Store the calculated potential flow bits directly
                f_decisions[link_key] = flow_bits

                nodes_data_busy.add(i)
                if not receiver_dev['is_sink']:
                # --- MODIFICATION END ---
                        nodes_data_busy.add(j)

    # Consolidate decisions
    actions = {
        'y': y_decisions,
        'e_hat': e_hat_decisions,
        'x': x_decisions,
        'p': p_decisions,
        'f': f_decisions
    }
    return actions

# --- EAG Scheduling Algorithm ---
def eag_scheduling(current_slot, nodes, sinks, potential_links, current_channel_gains,
                   noise_power_W, bandwidth, battery_max_J): # packet_details removed from args
    """
    Performs Energy-Aware Greedy (EAG) scheduling.
    Selects links greedily based on an energy-aware weight: W_lk = (q_ik - q_jk) * (b_i / b_max),
    subject to basic energy and half-duplex constraints. No energy cooperation.
    Args:
        current_slot: The current time slot index (t).
        nodes: List of node dictionaries (mutable state).
        sinks: List of sink dictionaries.
        potential_links: Dict {sender_id: [receiver_ids]}.
        current_channel_gains: Dict {(sender_id, receiver_id): inst_gain}.
        packet_details: Dict {packet_id: details}. Needed for packet selection.
        noise_power_W: Noise power in Watts.
        bandwidth: Channel bandwidth in Hz.
        battery_max_J: Maximum battery capacity (b_max).
    Returns:
        A dictionary containing the decisions for this timeslot (y and e_hat are always 0):
        {
            'y': {node_id: 0},                   # Energy Tx state (always 0)
            'e_hat': {node_id: 0.0},             # Energy Tx power (always 0)
            'x': {(i, j, k): 0 or 1},            # Link activation for sink k
            'p': {(i, j): power_W},              # Data Tx power
            'f': {(i, j, k): float_bits_value}   # Data flow (BITS, not packet IDs)
        }
    """
    # Initialize decisions - No Energy Cooperation
    y_decisions = {node['id']: 0 for node in nodes}
    e_hat_decisions = {node['id']: 0.0 for node in nodes} # Always 0 for EAG
    x_decisions = {}
    p_decisions = {}
    f_decisions = {}

    nodes_data_busy = set()
    candidate_links_tuples = [] # List of ((i, j, k), Weight_Value, f_potential_bits)

    # 1. Calculate Energy-Aware Weight for all potential links
    num_nodes = len(nodes)
    for sender_id in range(num_nodes):
        sender_node = get_device_by_id(sender_id, nodes, sinks)
        if not sender_node or sender_id not in potential_links: continue

        # Basic energy check: Can sender afford p_min?
        if sender_node['battery'] < P_MIN_W:
            continue

        # Calculate sender energy factor (b_i / b_max)
        # Add epsilon to avoid division by zero if b_max is somehow 0
        energy_factor = sender_node['battery'] / (battery_max_J + 1e-9)
        energy_factor = max(0, min(energy_factor, 1.0)) # Clamp between 0 and 1

        for receiver_id in potential_links[sender_id]:
            receiver_dev = get_device_by_id(receiver_id, nodes, sinks)
            if not receiver_dev: continue

            channel_gain_ij = current_channel_gains.get((sender_id, receiver_id), 0.0)
            if channel_gain_ij <= 0: continue

            for sink_k_id in sender_node['queue_bits']:
                # REMOVED: Check for non-empty packet list (sender_node['queues'])
                # Now rely on f_potential_bits > 0 check below
                p_trial_W = P_MIN_W

                f_potential_bits = calculate_potential_flow(sender_node, receiver_dev, sink_k_id, p_trial_W, channel_gain_ij, e_hat_j=0.0,
                                                                 noise_power_W=noise_power_W, bandwidth=bandwidth)

                if f_potential_bits > 1e-9:
                    receiver_energy_ok = True
                    if not receiver_dev['is_sink']:
                        required_rcv_energy = P_RCV_J_per_bit * f_potential_bits
                        receiver_energy_ok = receiver_dev['battery'] >= required_rcv_energy

                    if receiver_energy_ok:
                        # Calculate GMW part of the weight
                        q_ik = sender_node['queue_bits'].get(sink_k_id, 0.0)
                        q_jk = 0.0
                        if not receiver_dev['is_sink']:
                            q_jk = receiver_dev['queue_bits'].get(sink_k_id, 0.0)
                        gmw_weight_part = q_ik - q_jk

                        # Calculate EAG weight
                        eag_weight = gmw_weight_part * energy_factor

                        # Only consider links with positive GMW part (or adjust logic if needed)
                        # and positive final EAG weight
                        if gmw_weight_part > 0 and eag_weight > 0:
                            candidate_links_tuples.append(((sender_id, receiver_id, sink_k_id), eag_weight, f_potential_bits))

    # 2. Sort candidates by EAG Weight
    candidate_links_tuples.sort(key=lambda x: x[1], reverse=True)

    # 3. Iteratively select links (same logic as GMW, but uses EAG weights for sorting)
    for candidate in candidate_links_tuples:
        (i, j, k), weight, flow_bits = candidate # weight is eag_weight

        sender_node = get_device_by_id(i, nodes, sinks)
        receiver_dev = get_device_by_id(j, nodes, sinks)

        sender_busy = i in nodes_data_busy
        receiver_busy = (not receiver_dev['is_sink']) and (j in nodes_data_busy)

        if not sender_busy and not receiver_busy:
            p_actual_W = P_MIN_W

            sender_energy_ok = sender_node['battery'] >= p_actual_W
            receiver_energy_ok = True
            if not receiver_dev['is_sink']:
                 required_rcv_energy = P_RCV_J_per_bit * flow_bits
                 receiver_energy_ok = receiver_dev['battery'] >= required_rcv_energy

            if sender_energy_ok and receiver_energy_ok and flow_bits > 1e-9:
                # --- Packet selection logic removed - Using flow_bits directly ---

                # --- Store flow_bits directly for GMW/EAG/RAND ---
                link_key = (i, j, k)
                power_key = (i, j)
                x_decisions[link_key] = 1
                if power_key not in p_decisions:
                    p_decisions[power_key] = p_actual_W
                # Store the calculated potential flow bits directly
                f_decisions[link_key] = flow_bits

                nodes_data_busy.add(i)
                if not receiver_dev['is_sink']:
                # --- MODIFICATION END ---
                        nodes_data_busy.add(j)

    # Consolidate decisions
    actions = {
        'y': y_decisions,
        'e_hat': e_hat_decisions,
        'x': x_decisions,
        'p': p_decisions,
        'f': f_decisions
    }
    return actions


# --- RAND Scheduling Algorithm ---
def rand_scheduling(current_slot, nodes, sinks, potential_links, current_channel_gains,
                    noise_power_W, bandwidth): # packet_details removed from args
    """
    Performs Randomized (RAND) scheduling.
    Randomly selects links that satisfy basic energy and half-duplex constraints.
    Args:
        current_slot: The current time slot index (t).
        nodes: List of node dictionaries (mutable state).
        sinks: List of sink dictionaries.
        potential_links: Dict {sender_id: [receiver_ids]}.
        current_channel_gains: Dict {(sender_id, receiver_id): inst_gain}.
        packet_details: Dict {packet_id: details}. Needed for packet selection.
        noise_power_W: Noise power in Watts.
        bandwidth: Channel bandwidth in Hz.
    Returns:
        A dictionary containing the decisions for this timeslot (y and e_hat are always 0):
        {
            'y': {node_id: 0},                   # Energy Tx state (always 0)
            'e_hat': {node_id: 0.0},             # Energy Tx power (always 0)
            'x': {(i, j, k): 0 or 1},            # Link activation for sink k
            'p': {(i, j): power_W},              # Data Tx power
            'f': {(i, j, k): float_bits_value}   # Data flow (BITS, not packet IDs)
        }
    """
    # Initialize decisions - No Energy Cooperation
    y_decisions = {node['id']: 0 for node in nodes}
    e_hat_decisions = {node['id']: 0.0 for node in nodes} # Always 0 for RAND
    x_decisions = {}
    p_decisions = {}
    f_decisions = {}

    nodes_data_busy = set()
    feasible_links_tuples = [] # List of ((i, j, k), f_potential_bits)

    # 1. Identify all feasible links satisfying basic energy constraints
    num_nodes = len(nodes)
    for sender_id in range(num_nodes):
        sender_node = get_device_by_id(sender_id, nodes, sinks)
        if not sender_node or sender_id not in potential_links: continue

        # Basic sender energy check
        if sender_node['battery'] < P_MIN_W:
            continue

        for receiver_id in potential_links[sender_id]:
            receiver_dev = get_device_by_id(receiver_id, nodes, sinks)
            if not receiver_dev: continue

            channel_gain_ij = current_channel_gains.get((sender_id, receiver_id), 0.0)
            if channel_gain_ij <= 0: continue

            for sink_k_id in sender_node['queue_bits']:
                # REMOVED: Check for non-empty packet list (sender_node['queues'])
                # Now rely on f_potential_bits > 0 check below
                p_trial_W = P_MIN_W

                f_potential_bits = calculate_potential_flow(sender_node, receiver_dev, sink_k_id, p_trial_W, channel_gain_ij, e_hat_j=0.0,
                                                                 noise_power_W=noise_power_W, bandwidth=bandwidth)

                if f_potential_bits > 1e-9:
                    # Basic receiver energy check
                    receiver_energy_ok = True
                    if not receiver_dev['is_sink']:
                        required_rcv_energy = P_RCV_J_per_bit * f_potential_bits
                        receiver_energy_ok = receiver_dev['battery'] >= required_rcv_energy

                    if receiver_energy_ok:
                        # Add feasible link to the list
                        feasible_links_tuples.append(((sender_id, receiver_id, sink_k_id), f_potential_bits))

    # 2. Shuffle the list of feasible links randomly
    random.shuffle(feasible_links_tuples)

    # 3. Iterate through the shuffled list and activate links respecting half-duplex
    for candidate in feasible_links_tuples:
        (i, j, k), flow_bits = candidate

        sender_node = get_device_by_id(i, nodes, sinks)
        receiver_dev = get_device_by_id(j, nodes, sinks)

        # Check half-duplex constraint
        sender_busy = i in nodes_data_busy
        receiver_busy = (not receiver_dev['is_sink']) and (j in nodes_data_busy)

        if not sender_busy and not receiver_busy:
            # Re-check energy constraints just in case state changed (though unlikely in RAND)
            p_actual_W = P_MIN_W
            sender_energy_ok = sender_node['battery'] >= p_actual_W
            receiver_energy_ok = True
            if not receiver_dev['is_sink']:
                 required_rcv_energy = P_RCV_J_per_bit * flow_bits
                 receiver_energy_ok = receiver_dev['battery'] >= required_rcv_energy

            if sender_energy_ok and receiver_energy_ok and flow_bits > 1e-9:
                # --- Packet selection logic removed - Using flow_bits directly ---

                # --- Store flow_bits directly for GMW/EAG/RAND ---
                link_key = (i, j, k)
                power_key = (i, j)
                x_decisions[link_key] = 1
                if power_key not in p_decisions:
                    p_decisions[power_key] = p_actual_W
                # Store the calculated potential flow bits directly
                f_decisions[link_key] = flow_bits

                # Mark nodes as busy
                nodes_data_busy.add(i)
                if not receiver_dev['is_sink']:
                # --- MODIFICATION END ---
                        nodes_data_busy.add(j)

    # Consolidate decisions
    actions = {
        'y': y_decisions,
        'e_hat': e_hat_decisions,
        'x': x_decisions,
        'p': p_decisions,
        'f': f_decisions
    }
    return actions
